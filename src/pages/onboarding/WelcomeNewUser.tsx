
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { useNavigate } from "react-router-dom";

export default function WelcomeNewUser() {
  const { setCurrentStep } = useOnboarding();
  const navigate = useNavigate();

  const handleStartSetup = () => {
    setCurrentStep(2);
    navigate('/onboarding/create-company');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <Card className="w-full max-w-md mx-4">
        <CardHeader className="text-center pb-4">
          <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">C</span>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">Welcome to CONVX</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <p className="text-gray-600 text-lg">
            Let's get your restaurant data platform started.
          </p>
          <Button 
            onClick={handleStartSetup}
            className="w-full py-3 text-lg"
            size="lg"
          >
            Start Setup
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
