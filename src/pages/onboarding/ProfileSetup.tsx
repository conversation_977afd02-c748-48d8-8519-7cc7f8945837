
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useOnboarding } from "@/contexts/OnboardingContext";
import { useUserProfile } from "@/hooks/useUserProfile";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

export default function ProfileSetup() {
  const { completeOnboarding } = useOnboarding();
  const { profile, updateProfile } = useUserProfile();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    fullName: profile?.fullName || '',
    smsAlerts: profile?.smsAlerts ?? true,
    emailAlerts: profile?.emailAlerts ?? true
  });
  const [loading, setLoading] = useState(false);

  const handleGoToDashboard = async () => {
    if (!formData.fullName.trim()) {
      toast({
        title: "Missing information",
        description: "Please enter your full name.",
        variant: "destructive"
      });
      return;
    }
    
    setLoading(true);
    try {
      // Update profile with final details
      await updateProfile({
        fullName: formData.fullName,
        smsAlerts: formData.smsAlerts,
        emailAlerts: formData.emailAlerts
      });

      // Complete onboarding
      await completeOnboarding();
      
      toast({
        title: "Welcome!",
        description: "Your profile has been set up successfully.",
      });

      navigate('/');
    } catch (error) {
      console.error('Error completing profile setup:', error);
      toast({
        title: "Error",
        description: "Failed to complete setup. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100">
      <Card className="w-full max-w-md mx-4">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900">Complete Your Profile</CardTitle>
          <p className="text-gray-600">Just a few more details to get started</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="fullName">Full Name</Label>
            <Input
              id="fullName"
              placeholder="Enter your full name"
              value={formData.fullName}
              onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
              disabled={loading}
            />
          </div>

          <div className="space-y-4 pt-4">
            <h3 className="font-semibold text-gray-900">Notification Preferences</h3>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="smsAlerts" className="text-sm font-normal">
                SMS Alerts
              </Label>
              <Switch
                id="smsAlerts"
                checked={formData.smsAlerts}
                onCheckedChange={(checked) => setFormData({ ...formData, smsAlerts: checked })}
                disabled={loading}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="emailAlerts" className="text-sm font-normal">
                Email Alerts
              </Label>
              <Switch
                id="emailAlerts"
                checked={formData.emailAlerts}
                onCheckedChange={(checked) => setFormData({ ...formData, emailAlerts: checked })}
                disabled={loading}
              />
            </div>
          </div>

          <Button 
            onClick={handleGoToDashboard}
            className="w-full mt-6 bg-green-600 hover:bg-green-700"
            disabled={!formData.fullName.trim() || loading}
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Setting up...
              </>
            ) : (
              'Complete Setup'
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
