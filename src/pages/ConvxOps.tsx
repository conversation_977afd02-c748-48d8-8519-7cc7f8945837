
import { AdminSidebar } from "@/components/AdminSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ConvxOpsLogo } from "@/components/ConvxOpsLogo";
import { ExternalLink } from "lucide-react";

export default function ConvxOps() {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      <div className="flex-1 flex flex-col">
        <DashboardHeader />
        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            <a 
              href="https://convx-op-sage.lovable.app/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center gap-3 mb-6 group hover:bg-blue-50 p-4 rounded-lg transition-colors border-2 border-transparent hover:border-blue-200"
            >
              <ConvxOpsLogo width={48} height={48} />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h1 className="text-3xl font-bold text-gray-900 group-hover:text-blue-700">CONVX Ops</h1>
                  <ExternalLink className="w-5 h-5 text-gray-400 group-hover:text-blue-600" />
                </div>
                <p className="text-gray-600 mt-1 group-hover:text-blue-600">Operations management and monitoring</p>
              </div>
            </a>

            <Card>
              <CardHeader>
                <CardTitle>Operations Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  This section will contain operations management tools and monitoring capabilities.
                  More functionality will be added soon.
                </p>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
