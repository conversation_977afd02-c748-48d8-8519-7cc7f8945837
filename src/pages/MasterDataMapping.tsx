import { useState } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AdminSidebar } from "@/components/AdminSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { LocationsTab } from "@/components/master-data/LocationsTab";
import { MenuItemsTab } from "@/components/master-data/MenuItemsTab";
import { MenuCategoriesTab } from "@/components/master-data/MenuCategoriesTab";
import { MenuProductsTab } from "@/components/master-data/MenuProductsTab";
import { IngredientsSuppliesTab } from "@/components/master-data/IngredientsSuppliesTab";
import { EmployeesTab } from "@/components/master-data/EmployeesTab";
import { DatesTab } from "@/components/master-data/DatesTab";
import { DayPartsTab } from "@/components/master-data/DayPartsTab";
import { RestaurantOperatorsTab } from "@/components/master-data/RestaurantOperatorsTab";
import { SurveyQuestionsTab } from "@/components/master-data/SurveyQuestionsTab";
import { FoodSalesTab } from "@/components/master-data/FoodSalesTab";
import { UnmappedItemsTab } from "@/components/master-data/UnmappedItemsTab";
import { DataMappingGuide } from "@/components/master-data/DataMappingGuide";
import { BrandedPageHeader } from "@/components/BrandedPageHeader";
import { BrandFooter } from "@/components/BrandFooter";
import { PageWalkthrough } from "@/components/walkthrough/PageWalkthrough";
import { masterDataWalkthroughSteps } from "@/data/masterDataWalkthroughSteps";
import { Database } from "lucide-react";

export default function MasterDataMapping() {
  const [selectedTab, setSelectedTab] = useState("locations");

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-orange-50 via-white to-orange-100">
        <AdminSidebar />
        <main className="flex-1 flex flex-col">
          <DashboardHeader />
          <div className="flex-1 p-6 space-y-6">
            <BrandedPageHeader
              title="Master Data Management"
              description="Organize and standardize your restaurant data sources. Ensure all your data is properly categorized and mapped for optimal analytics and reporting."
              icon={Database}
              className="master-data-header"
            />

            <div className="bg-white rounded-2xl border-2 border-orange-200 shadow-lg shadow-orange-100/50 overflow-hidden">
              <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
                <div className="bg-gradient-to-r from-orange-100 to-orange-50 border-b-2 border-orange-200 p-6">
                  <TabsList className="master-data-tabs grid w-full grid-cols-6 lg:grid-cols-12 gap-1 bg-white border-2 border-orange-200 p-1">
                    <TabsTrigger value="locations" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Locations</TabsTrigger>
                    <TabsTrigger value="menu-items" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Menu Items</TabsTrigger>
                    <TabsTrigger value="menu-categories" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Categories</TabsTrigger>
                    <TabsTrigger value="menu-products" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Products</TabsTrigger>
                    <TabsTrigger value="ingredients" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Ingredients</TabsTrigger>
                    <TabsTrigger value="employees" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Employees</TabsTrigger>
                    <TabsTrigger value="dates" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Dates</TabsTrigger>
                    <TabsTrigger value="day-parts" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Day Parts</TabsTrigger>
                    <TabsTrigger value="operators" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Operators</TabsTrigger>
                    <TabsTrigger value="surveys" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Surveys</TabsTrigger>
                    <TabsTrigger value="food-sales" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Food Sales</TabsTrigger>
                    <TabsTrigger value="unmapped" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs">Unmapped</TabsTrigger>
                  </TabsList>
                </div>

                <div className="p-6">
                  <TabsContent value="locations">
                    <LocationsTab />
                  </TabsContent>
                  <TabsContent value="menu-items">
                    <MenuItemsTab />
                  </TabsContent>
                  <TabsContent value="menu-categories">
                    <MenuCategoriesTab />
                  </TabsContent>
                  <TabsContent value="menu-products">
                    <MenuProductsTab />
                  </TabsContent>
                  <TabsContent value="ingredients">
                    <IngredientsSuppliesTab />
                  </TabsContent>
                  <TabsContent value="employees">
                    <EmployeesTab />
                  </TabsContent>
                  <TabsContent value="dates">
                    <DatesTab />
                  </TabsContent>
                  <TabsContent value="day-parts">
                    <DayPartsTab />
                  </TabsContent>
                  <TabsContent value="operators">
                    <RestaurantOperatorsTab />
                  </TabsContent>
                  <TabsContent value="surveys">
                    <SurveyQuestionsTab />
                  </TabsContent>
                  <TabsContent value="food-sales">
                    <FoodSalesTab />
                  </TabsContent>
                  <TabsContent value="unmapped">
                    <UnmappedItemsTab />
                  </TabsContent>
                </div>
              </Tabs>
            </div>

            <DataMappingGuide dataType={selectedTab} />

            <BrandFooter />
          </div>
        </main>
        
        <PageWalkthrough
          steps={masterDataWalkthroughSteps}
          pageKey="master-data"
        />
      </div>
    </SidebarProvider>
  );
}
