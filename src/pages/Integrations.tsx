
import { useState } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AdminSidebar } from "@/components/AdminSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { IntegrationWizard } from "@/components/integrations/IntegrationWizard";
import { IntegrationStats } from "@/components/integrations/IntegrationStats";
import { IntegrationsTable } from "@/components/integrations/IntegrationsTable";
import { DataUploadsTable } from "@/components/integrations/DataUploadsTable";
import { BrandedPageHeader } from "@/components/BrandedPageHeader";
import { BrandFooter } from "@/components/BrandFooter";
import { Button } from "@/components/ui/button";
import { PageWalkthrough } from "@/components/walkthrough/PageWalkthrough";
import { integrationsWalkthroughSteps } from "@/data/integrationsWalkthroughSteps";
import { Zap, Plus } from "lucide-react";

export default function Integrations() {
  const [showWizard, setShowWizard] = useState(false);

  if (showWizard) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-gradient-to-br from-orange-50 via-white to-orange-100">
          <AdminSidebar />
          <main className="flex-1 flex flex-col">
            <DashboardHeader />
            <div className="flex-1 p-6">
              <IntegrationWizard onClose={() => setShowWizard(false)} />
            </div>
          </main>
        </div>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-orange-50 via-white to-orange-100">
        <AdminSidebar />
        <main className="flex-1 flex flex-col">
          <DashboardHeader />
          <div className="flex-1 p-6 space-y-6">
            <BrandedPageHeader
              title="Data Integrations Hub"
              description="Connect your restaurant systems and data sources to CONVX Data. Seamlessly integrate POS systems, inventory management, and third-party platforms."
              icon={Zap}
              className="integrations-header"
            >
              <Button
                onClick={() => setShowWizard(true)}
                className="bg-white text-orange-600 hover:bg-orange-50 border-2 border-white hover:border-orange-200 font-bold text-lg px-8 py-4 h-auto shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                data-tour="add-integration"
              >
                <Plus className="w-6 h-6 mr-3" />
                Add New Integration
              </Button>
            </BrandedPageHeader>

            <IntegrationStats />
            
            <div className="space-y-6">
              <IntegrationsTable />
              <DataUploadsTable />
            </div>

            <BrandFooter />
          </div>
        </main>
        
        <PageWalkthrough
          steps={integrationsWalkthroughSteps}
          pageKey="integrations"
        />
      </div>
    </SidebarProvider>
  );
}
