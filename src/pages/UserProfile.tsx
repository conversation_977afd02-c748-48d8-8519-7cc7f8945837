
import { useState } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AdminSidebar } from "@/components/AdminSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { BrandedPageHeader } from "@/components/BrandedPageHeader";
import { BrandFooter } from "@/components/BrandFooter";
import { ProfileOverviewCard } from "@/components/profile/ProfileOverviewCard";
import { ProfileDetailsCard } from "@/components/profile/ProfileDetailsCard";
import { useUserProfile } from "@/hooks/useUserProfile";
import { useCompany } from "@/hooks/useCompany";
import { useToast } from "@/hooks/use-toast";
import { User } from "lucide-react";

export default function UserProfile() {
  const { profile, updateProfile } = useUserProfile();
  const { company } = useCompany();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    fullName: profile?.fullName || '',
    email: profile?.email || '',
    role: profile?.role || 'Business Admin' as const,
    smsAlerts: profile?.smsAlerts ?? true,
    emailAlerts: profile?.emailAlerts ?? true,
    phone: '',
    timezone: 'America/New_York'
  });

  const [isEditing, setIsEditing] = useState(false);

  const handleSave = async () => {
    try {
      await updateProfile({
        fullName: formData.fullName,
        role: formData.role,
        smsAlerts: formData.smsAlerts,
        emailAlerts: formData.emailAlerts
      });
      
      setIsEditing(false);
      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-orange-50 via-white to-orange-100">
        <AdminSidebar />
        <main className="flex-1 flex flex-col">
          <DashboardHeader />
          <div className="flex-1 p-6 space-y-6">
            <BrandedPageHeader
              title="User Profile"
              description="Manage your account settings, preferences, and notification settings. Keep your profile information up to date for the best CONVX Data Kitchen experience."
              icon={User}
              className="profile-header"
            />

            <div className="bg-white rounded-2xl border-2 border-orange-200 shadow-lg shadow-orange-100/50 p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <ProfileOverviewCard
                  fullName={formData.fullName}
                  email={formData.email}
                  role={formData.role}
                  companyName={company?.name}
                  companyIndustry={company?.industry}
                />

                <ProfileDetailsCard
                  formData={formData}
                  isEditing={isEditing}
                  onFormDataChange={setFormData}
                  onEditToggle={handleEditToggle}
                  onSave={handleSave}
                />
              </div>
            </div>

            <BrandFooter />
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
}
