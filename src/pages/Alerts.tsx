
import { useState } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AdminSidebar } from "@/components/AdminSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { AlertsTable } from "@/components/alerts/AlertsTable";
import { AlertsFilters } from "@/components/alerts/AlertsFilters";
import { BrandedPageHeader } from "@/components/BrandedPageHeader";
import { BrandFooter } from "@/components/BrandFooter";
import { PageWalkthrough } from "@/components/walkthrough/PageWalkthrough";
import { alertsWalkthroughSteps } from "@/data/alertsWalkthroughSteps";
import { AlertTriangle, Filter } from "lucide-react";

export interface AlertFilters {
  severity: string[];
  status: string[];
  locations: string[];
}

const Alerts = () => {
  const [filters, setFilters] = useState<AlertFilters>({
    severity: [],
    status: [],
    locations: []
  });

  const [selectedAlerts, setSelectedAlerts] = useState<string[]>([]);

  const handleFilterChange = (category: keyof AlertFilters, value: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      [category]: checked 
        ? [...prev[category], value]
        : prev[category].filter(item => item !== value)
    }));
  };

  const handleClearAllFilters = () => {
    setFilters({
      severity: [],
      status: [],
      locations: []
    });
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-orange-50 via-white to-orange-100">
        <AdminSidebar />
        <main className="flex-1 flex flex-col">
          <DashboardHeader />
          <div className="flex-1 p-6 space-y-6">
            <BrandedPageHeader
              title="Alert Center"
              description="Monitor and manage alerts from all your data sources and system processes. Stay informed about data quality, sync issues, and system health."
              icon={AlertTriangle}
              className="alerts-header"
            />
            
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <div className="lg:col-span-1 alerts-filters">
                <div className="bg-white rounded-2xl border-2 border-orange-200 shadow-lg shadow-orange-100/50 overflow-hidden">
                  <div className="bg-gradient-to-r from-orange-100 to-orange-50 border-b-2 border-orange-200 px-6 py-4">
                    <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <Filter className="w-5 h-5 text-orange-600" />
                      Filters
                    </h2>
                  </div>
                  <div className="p-6">
                    <AlertsFilters 
                      filters={filters}
                      onFilterChange={handleFilterChange}
                      onClearAll={handleClearAllFilters}
                    />
                  </div>
                </div>
              </div>
              <div className="lg:col-span-3 alerts-table">
                <div className="bg-white rounded-2xl border-2 border-orange-200 shadow-lg shadow-orange-100/50 overflow-hidden">
                  <div className="bg-gradient-to-r from-orange-100 to-orange-50 border-b-2 border-orange-200 px-6 py-4">
                    <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <AlertTriangle className="w-5 h-5 text-orange-600" />
                      Active Alerts
                    </h2>
                  </div>
                  <div className="p-6">
                    <AlertsTable 
                      filters={filters}
                      selectedAlerts={selectedAlerts}
                      onSelectionChange={setSelectedAlerts}
                    />
                  </div>
                </div>
              </div>
            </div>

            <BrandFooter />
          </div>
        </main>
        
        <PageWalkthrough
          steps={alertsWalkthroughSteps}
          pageKey="alerts"
        />
      </div>
    </SidebarProvider>
  );
};

export default Alerts;
