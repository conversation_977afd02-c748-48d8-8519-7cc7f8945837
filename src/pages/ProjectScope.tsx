
import React from 'react';
import { ProjectScopeExport } from '@/components/ProjectScopeExport';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, Clock, CheckCircle, Users } from 'lucide-react';

export default function ProjectScope() {
  const projectStats = {
    totalTasks: 39,
    totalHours: 612,
    duration: '63 days',
    teamSize: 6,
    completionRate: '100%'
  };

  const taskCategories = [
    { name: 'Executive Dashboard & Analytics', tasks: 5, hours: 68, color: 'bg-blue-100 text-blue-800' },
    { name: 'Master Data Management', tasks: 5, hours: 100, color: 'bg-green-100 text-green-800' },
    { name: 'Data Integrations Hub', tasks: 5, hours: 88, color: 'bg-purple-100 text-purple-800' },
    { name: 'Specialized Business Modules', tasks: 4, hours: 76, color: 'bg-orange-100 text-orange-800' },
    { name: 'User Experience & Onboarding', tasks: 6, hours: 88, color: 'bg-pink-100 text-pink-800' },
    { name: 'Business Intelligence Features', tasks: 5, hours: 64, color: 'bg-indigo-100 text-indigo-800' },
    { name: 'Quality Assurance', tasks: 5, hours: 92, color: 'bg-red-100 text-red-800' },
    { name: 'Deployment & Documentation', tasks: 4, hours: 36, color: 'bg-gray-100 text-gray-800' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            CONVX Data Platform - Project Scope
          </h1>

          {/* Project Overview */}
          <div className="grid md:grid-cols-5 gap-4 mb-8">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{projectStats.totalTasks}</div>
                <div className="text-sm text-gray-600">Total Tasks</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <Clock className="w-8 h-8 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{projectStats.totalHours}</div>
                <div className="text-sm text-gray-600">Total Hours</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <CalendarDays className="w-8 h-8 text-purple-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{projectStats.duration}</div>
                <div className="text-sm text-gray-600">Duration</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <Users className="w-8 h-8 text-orange-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{projectStats.teamSize}</div>
                <div className="text-sm text-gray-600">Team Members</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-green-600">{projectStats.completionRate}</div>
                <div className="text-sm text-gray-600">Complete</div>
              </CardContent>
            </Card>
          </div>

          {/* Task Categories Breakdown */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Task Categories Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {taskCategories.map((category, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">{category.name}</h4>
                      <p className="text-sm text-gray-600">{category.tasks} tasks • {category.hours} hours</p>
                    </div>
                    <Badge className={category.color}>
                      Completed
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Export Section */}
          <ProjectScopeExport />

          {/* Project Timeline Summary */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Project Timeline Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Project Start Date:</span>
                  <span className="text-gray-600">January 15, 2024</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Project End Date:</span>
                  <span className="text-gray-600">April 16, 2024</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Development Time:</span>
                  <span className="text-gray-600">612 hours (76.5 days)</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">Project Status:</span>
                  <Badge className="bg-green-100 text-green-800">Completed</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
