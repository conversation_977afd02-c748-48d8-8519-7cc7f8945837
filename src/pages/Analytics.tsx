
import React, { useState } from 'react';
import { SidebarProvider } from "@/components/ui/sidebar";
import { AdminSidebar } from "@/components/AdminSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { BrandedPageHeader } from "@/components/BrandedPageHeader";
import { BrandFooter } from "@/components/BrandFooter";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AnalyticsDashboard } from '@/components/analytics/AnalyticsDashboard';
import { EnhancedAnalyticsDashboard } from '@/components/analytics/EnhancedAnalyticsDashboard';
import { AIInsightsDashboard } from '@/components/analytics/AIInsightsDashboard';
import { ETLControlPanel } from '@/components/analytics/ETLControlPanel';
import { BarChart3, Brain, TrendingUp, BarChart2, Database } from 'lucide-react';

export default function Analytics() {
  const [selectedLocation, setSelectedLocation] = useState<string>();
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>();

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-orange-50 via-white to-orange-100">
        <AdminSidebar />
        <main className="flex-1 flex flex-col">
          <DashboardHeader />
          <div className="flex-1 p-6 space-y-6">
            <BrandedPageHeader
              title="Analytics Dashboard"
              description="Advanced analytics and AI insights for data-driven decision making. Transform your restaurant data into actionable business intelligence."
              icon={BarChart3}
              className="analytics-header"
            />

            <div className="bg-white rounded-2xl border-2 border-orange-200 shadow-lg shadow-orange-100/50 p-6">
              <Tabs defaultValue="ai-insights" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4 h-14 p-1 bg-orange-50 border-2 border-orange-200 rounded-xl">
                  <TabsTrigger 
                    value="ai-insights" 
                    className="h-12 text-sm font-semibold rounded-lg transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-orange-700 data-[state=active]:shadow-md data-[state=active]:border-orange-300 hover:bg-orange-100/50 flex items-center gap-2"
                  >
                    <Brain className="h-4 w-4" />
                    AI Insights
                  </TabsTrigger>
                  <TabsTrigger 
                    value="executive" 
                    className="h-12 text-sm font-semibold rounded-lg transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-orange-700 data-[state=active]:shadow-md data-[state=active]:border-orange-300 hover:bg-orange-100/50 flex items-center gap-2"
                  >
                    <TrendingUp className="h-4 w-4" />
                    Executive Dashboard
                  </TabsTrigger>
                  <TabsTrigger 
                    value="operational" 
                    className="h-12 text-sm font-semibold rounded-lg transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-orange-700 data-[state=active]:shadow-md data-[state=active]:border-orange-300 hover:bg-orange-100/50 flex items-center gap-2"
                  >
                    <BarChart2 className="h-4 w-4" />
                    Operational Analytics
                  </TabsTrigger>
                  <TabsTrigger 
                    value="etl" 
                    className="h-12 text-sm font-semibold rounded-lg transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-orange-700 data-[state=active]:shadow-md data-[state=active]:border-orange-300 hover:bg-orange-100/50 flex items-center gap-2"
                  >
                    <Database className="h-4 w-4" />
                    Data Management
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="ai-insights">
                  <AIInsightsDashboard tenantKey={1} />
                </TabsContent>

                <TabsContent value="executive">
                  <EnhancedAnalyticsDashboard 
                    locationId={selectedLocation} 
                    dateRange={dateRange} 
                  />
                </TabsContent>

                <TabsContent value="operational">
                  <AnalyticsDashboard 
                    locationId={selectedLocation} 
                    dateRange={dateRange} 
                  />
                </TabsContent>

                <TabsContent value="etl">
                  <ETLControlPanel />
                </TabsContent>
              </Tabs>
            </div>

            <BrandFooter />
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
}
