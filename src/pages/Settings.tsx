
import { useState } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AdminSidebar } from "@/components/AdminSidebar";
import { DashboardHeader } from "@/components/DashboardHeader";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { TeamMembersTable } from "@/components/user-management/TeamMembersTable";
import { RoleBasedComponent } from "@/components/user-management/RoleBasedComponent";
import { NotificationSettings } from "@/components/profile/NotificationSettings";
import { BrandedPageHeader } from "@/components/BrandedPageHeader";
import { BrandFooter } from "@/components/BrandFooter";
import { useUserProfile } from "@/hooks/useUserProfile";
import { useToast } from "@/hooks/use-toast";
import { ChefHat, Settings as SettingsIcon, Users } from "lucide-react";

export default function Settings() {
  const { profile, updateProfile } = useUserProfile();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  
  const [formData, setFormData] = useState({
    emailAlerts: profile?.emailAlerts ?? true,
    smsAlerts: profile?.smsAlerts ?? true,
  });

  const handleSave = async () => {
    try {
      await updateProfile({
        emailAlerts: formData.emailAlerts,
        smsAlerts: formData.smsAlerts,
      });
      
      setIsEditing(false);
      toast({
        title: "Settings Updated",
        description: "Your notification settings have been successfully updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-orange-50 via-white to-orange-100">
        <AdminSidebar />
        <main className="flex-1 flex flex-col">
          <DashboardHeader />
          <div className="flex-1 p-6 space-y-6">
            <BrandedPageHeader
              title="Platform Settings"
              description="Configure your data platform preferences. Manage account settings and team member access to optimize your data analytics experience."
              icon={SettingsIcon}
            />

            <div className="bg-white rounded-2xl border-2 border-orange-200 shadow-lg shadow-orange-100/50 overflow-hidden">
              <Tabs defaultValue="notifications" className="w-full">
                <div className="bg-gradient-to-r from-orange-100 to-orange-50 border-b-2 border-orange-200 px-6 py-4">
                  <TabsList className="grid w-full grid-cols-3 bg-white border-2 border-orange-200">
                    <TabsTrigger value="notifications" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white">
                      Data Notifications
                    </TabsTrigger>
                    <TabsTrigger value="team" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white">
                      Team Members
                    </TabsTrigger>
                    <TabsTrigger value="account" className="data-[state=active]:bg-orange-500 data-[state=active]:text-white">
                      Account Settings
                    </TabsTrigger>
                  </TabsList>
                </div>
                
                <TabsContent value="notifications" className="space-y-6 p-8">
                  <div className="bg-gradient-to-r from-orange-50 to-white rounded-xl border-2 border-orange-200 p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <ChefHat className="w-5 h-5 text-orange-600" />
                      <h3 className="text-lg font-semibold text-gray-800">Data Alert Preferences</h3>
                    </div>
                    <NotificationSettings
                      emailAlerts={formData.emailAlerts}
                      smsAlerts={formData.smsAlerts}
                      isEditing={isEditing}
                      onEmailAlertsChange={(checked) => 
                        setFormData(prev => ({ ...prev, emailAlerts: checked }))
                      }
                      onSmsAlertsChange={(checked) => 
                        setFormData(prev => ({ ...prev, smsAlerts: checked }))
                      }
                    />
                    
                    <div className="flex justify-end space-x-2 mt-6">
                      {isEditing ? (
                        <>
                          <button
                            onClick={() => setIsEditing(false)}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border-2 border-gray-300 rounded-md hover:bg-gray-50"
                          >
                            Cancel
                          </button>
                          <button
                            onClick={handleSave}
                            className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-orange-600 border-2 border-transparent rounded-md hover:from-orange-600 hover:to-orange-700"
                          >
                            Save Changes
                          </button>
                        </>
                      ) : (
                        <button
                          onClick={() => setIsEditing(true)}
                          className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-orange-600 border-2 border-transparent rounded-md hover:from-orange-600 hover:to-orange-700"
                        >
                          Edit Settings
                        </button>
                      )}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="team" className="space-y-6 p-8">
                  <RoleBasedComponent 
                    allowedRoles={['CEO', 'COO']}
                    fallback={
                      <div className="text-center py-8 text-gray-500 bg-gradient-to-r from-orange-50 to-white rounded-xl border-2 border-orange-200 p-6">
                        <Users className="w-12 h-12 text-orange-300 mx-auto mb-4" />
                        <p>You don't have permission to manage team members.</p>
                      </div>
                    }
                  >
                    <div className="bg-gradient-to-r from-orange-50 to-white rounded-xl border-2 border-orange-200 p-6">
                      <div className="flex items-center gap-2 mb-4">
                        <Users className="w-5 h-5 text-orange-600" />
                        <h3 className="text-lg font-semibold text-gray-800">Team Management</h3>
                      </div>
                      <TeamMembersTable />
                    </div>
                  </RoleBasedComponent>
                </TabsContent>
                
                <TabsContent value="account" className="space-y-6 p-8">
                  <div className="text-center py-8 text-gray-500 bg-gradient-to-r from-orange-50 to-white rounded-xl border-2 border-orange-200 p-6">
                    <ChefHat className="w-12 h-12 text-orange-300 mx-auto mb-4" />
                    <p>Account settings coming soon...</p>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            <BrandFooter />
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
}
