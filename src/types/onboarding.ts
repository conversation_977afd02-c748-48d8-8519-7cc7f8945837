
export interface User {
  id: string;
  email: string;
  fullName?: string;
  role: 'CEO' | 'COO' | 'Data Admin' | 'Business Admin';
  companyId?: string;
  invitedBy?: string;
  hasCompletedOnboarding: boolean;
  onboardingStep?: number;
  smsAlerts?: boolean;
  emailAlerts?: boolean;
}

export interface Company {
  id: string;
  name: string;
  industry: 'Restaurant' | 'Hospitality' | 'Food Service' | 'Other';
  createdBy: string;
  createdAt: Date;
}

export interface TeamMember {
  email: string;
  role: 'Data Admin' | 'Business Admin';
  inviteStatus: 'pending' | 'accepted';
}

export interface OnboardingContextType {
  currentUser: User | null;
  company: Company | null;
  isNewUser: boolean;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  setCurrentUser: (user: User) => void;
  setCompany: (company: Company) => void;
  completeOnboarding: () => void;
}
