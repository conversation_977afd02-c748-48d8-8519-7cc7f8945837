
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface ExecutiveMetrics {
  financial: {
    totalRevenue: number;
    grossMargin: number;
    netMargin: number;
    revenueGrowth: number;
    costOfGoodsSold: number;
    operatingExpenses: number;
  };
  operational: {
    averageOrderValue: number;
    customerCount: number;
    transactionCount: number;
    averageServiceTime: number;
    customerSatisfactionScore: number;
    tableUtilization: number;
  };
  strategic: {
    sameStoreSalesGrowth: number;
    customerLifetimeValue: number;
    marketShare: number;
    brandSentiment: number;
    newCustomerAcquisition: number;
    customerRetentionRate: number;
  };
  riskIndicators: {
    laborVariance: number;
    inventoryWaste: number;
    foodCostVariance: number;
    cashFlowRisk: number;
    complianceScore: number;
    equipmentMaintenanceScore: number;
  };
}

interface LocationPerformance {
  locationKey: number;
  locationName: string;
  revenue: number;
  revenueGrowth: number;
  profitMargin: number;
  customerCount: number;
  averageOrderValue: number;
  performanceScore: number;
  riskScore: number;
  alerts: string[];
}

interface TrendData {
  period: string;
  revenue: number;
  profit: number;
  customerCount: number;
  averageOrderValue: number;
  laborCost: number;
  foodCost: number;
}

export const useExecutiveMetrics = (tenantKey: number = 1, dateRange?: { start: string; end: string }) => {
  return useQuery({
    queryKey: ['executive-metrics', tenantKey, dateRange],
    queryFn: async (): Promise<ExecutiveMetrics> => {
      console.log('Fetching executive metrics...');
      
      // Mock comprehensive executive metrics data
      return {
        financial: {
          totalRevenue: 2847650.00,
          grossMargin: 58.7,
          netMargin: 12.3,
          revenueGrowth: 15.2,
          costOfGoodsSold: 1175000.00,
          operatingExpenses: 1320500.00
        },
        operational: {
          averageOrderValue: 68.12,
          customerCount: 41780,
          transactionCount: 34560,
          averageServiceTime: 18.5,
          customerSatisfactionScore: 4.3,
          tableUtilization: 72.8
        },
        strategic: {
          sameStoreSalesGrowth: 8.9,
          customerLifetimeValue: 486.50,
          marketShare: 12.8,
          brandSentiment: 78.5,
          newCustomerAcquisition: 2340,
          customerRetentionRate: 68.9
        },
        riskIndicators: {
          laborVariance: 3.2,
          inventoryWaste: 4.8,
          foodCostVariance: 2.1,
          cashFlowRisk: 15.7,
          complianceScore: 94.2,
          equipmentMaintenanceScore: 87.6
        }
      };
    },
    enabled: true
  });
};

export const useLocationPerformance = (tenantKey: number = 1) => {
  return useQuery({
    queryKey: ['location-performance', tenantKey],
    queryFn: async (): Promise<LocationPerformance[]> => {
      console.log('Fetching location performance...');
      
      // Mock location performance data
      return [
        {
          locationKey: 1,
          locationName: 'Downtown Location',
          revenue: 485720.00,
          revenueGrowth: 12.5,
          profitMargin: 14.8,
          customerCount: 8960,
          averageOrderValue: 54.20,
          performanceScore: 92.3,
          riskScore: 18.5,
          alerts: ['High labor variance', 'Equipment maintenance due']
        },
        {
          locationKey: 2,
          locationName: 'Mall Location',
          revenue: 398450.00,
          revenueGrowth: 8.7,
          profitMargin: 11.2,
          customerCount: 7850,
          averageOrderValue: 50.75,
          performanceScore: 87.6,
          riskScore: 24.3,
          alerts: ['Inventory waste above target']
        },
        {
          locationKey: 3,
          locationName: 'Airport Location',
          revenue: 672890.00,
          revenueGrowth: 18.9,
          profitMargin: 16.4,
          customerCount: 12450,
          averageOrderValue: 54.05,
          performanceScore: 96.8,
          riskScore: 12.1,
          alerts: []
        }
      ];
    },
    enabled: true
  });
};

export const useTrendAnalysis = (tenantKey: number = 1, period: 'daily' | 'weekly' | 'monthly' = 'daily') => {
  return useQuery({
    queryKey: ['trend-analysis', tenantKey, period],
    queryFn: async (): Promise<TrendData[]> => {
      console.log('Fetching trend analysis...');
      
      // Mock trend data for the last 30 days
      const data: TrendData[] = [];
      for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        
        data.push({
          period: date.toISOString().split('T')[0],
          revenue: 95000 + Math.random() * 20000,
          profit: 11000 + Math.random() * 5000,
          customerCount: 1300 + Math.random() * 400,
          averageOrderValue: 65 + Math.random() * 15,
          laborCost: 28000 + Math.random() * 8000,
          foodCost: 38000 + Math.random() * 10000
        });
      }
      
      return data;
    },
    enabled: true
  });
};

export const useAnomalyDetection = (tenantKey: number = 1) => {
  return useQuery({
    queryKey: ['anomaly-detection', tenantKey],
    queryFn: async () => {
      console.log('Fetching anomaly detection...');
      
      return {
        anomalies: [
          {
            id: 1,
            type: 'revenue',
            severity: 'high',
            description: 'Revenue 23% below forecast for Downtown Location',
            detectedAt: '2024-01-07T10:30:00Z',
            location: 'Downtown Location',
            impact: 'High',
            recommendedAction: 'Investigate staffing levels and promotions'
          },
          {
            id: 2,
            type: 'cost',
            severity: 'medium',
            description: 'Food cost variance 8% above target',
            detectedAt: '2024-01-07T09:15:00Z',
            location: 'All Locations',
            impact: 'Medium',
            recommendedAction: 'Review supplier contracts and portion control'
          },
          {
            id: 3,
            type: 'customer',
            severity: 'low',
            description: 'Customer satisfaction dip in evening hours',
            detectedAt: '2024-01-07T08:45:00Z',
            location: 'Mall Location',
            impact: 'Low',
            recommendedAction: 'Review evening staff training needs'
          }
        ]
      };
    },
    enabled: true
  });
};
