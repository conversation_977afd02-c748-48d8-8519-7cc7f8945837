
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface SalesAnalytics {
  totalSales: number;
  transactionCount: number;
  averageTicket: number;
  topMenuItems: Array<{
    item_name: string;
    total_sales: number;
    quantity_sold: number;
  }>;
  salesByDayPart: Array<{
    day_part: string;
    total_sales: number;
    transaction_count: number;
  }>;
  salesTrend: Array<{
    date: string;
    sales: number;
    transactions: number;
  }>;
}

export const useAnalytics = (locationId?: string, dateRange?: { start: string; end: string }) => {
  return useQuery({
    queryKey: ['analytics', locationId, dateRange],
    queryFn: async (): Promise<SalesAnalytics> => {
      console.log('Fetching analytics data...');
      
      // For now, return mock data since we need to set up the ETL process
      // In a real implementation, this would query the fact tables
      return {
        totalSales: 125750.50,
        transactionCount: 1847,
        averageTicket: 68.12,
        topMenuItems: [
          { item_name: 'Classic Burger', total_sales: 15430.25, quantity_sold: 342 },
          { item_name: 'Margherita Pizza', total_sales: 12890.75, quantity_sold: 287 },
          { item_name: 'Caesar Salad', total_sales: 8945.50, quantity_sold: 245 },
          { item_name: 'Fish & Chips', total_sales: 7820.00, quantity_sold: 156 },
          { item_name: 'Chocolate Brownie', total_sales: 6750.25, quantity_sold: 225 }
        ],
        salesByDayPart: [
          { day_part: 'breakfast', total_sales: 18500.25, transaction_count: 285 },
          { day_part: 'lunch', total_sales: 52340.75, transaction_count: 847 },
          { day_part: 'dinner', total_sales: 48920.50, transaction_count: 623 },
          { day_part: 'late_night', total_sales: 5989.00, transaction_count: 92 }
        ],
        salesTrend: [
          { date: '2024-01-01', sales: 8450.25, transactions: 125 },
          { date: '2024-01-02', sales: 9120.75, transactions: 142 },
          { date: '2024-01-03', sales: 7890.50, transactions: 118 },
          { date: '2024-01-04', sales: 10250.25, transactions: 156 },
          { date: '2024-01-05', sales: 11340.75, transactions: 167 },
          { date: '2024-01-06', sales: 12890.50, transactions: 189 },
          { date: '2024-01-07', sales: 9750.25, transactions: 143 }
        ]
      };
    },
    enabled: true
  });
};

export const useDataWarehouseSync = () => {
  return useQuery({
    queryKey: ['dw-sync-status'],
    queryFn: async () => {
      console.log('Checking data warehouse sync status...');
      
      // Check the latest sync status from operational to analytical tables
      const { data: syncLog } = await supabase
        .from('data_ingestion_logs')
        .select('*')
        .eq('source_type', 'etl_process')
        .order('started_at', { ascending: false })
        .limit(1)
        .single();

      return {
        lastSync: syncLog?.completed_at || null,
        status: syncLog?.status || 'pending',
        recordsProcessed: syncLog?.records_processed || 0,
        nextSync: new Date(Date.now() + 3600000).toISOString() // 1 hour from now
      };
    }
  });
};
