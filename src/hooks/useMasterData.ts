
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';
import { useAuth } from '@/hooks/useAuth';

type DataType = Database["public"]["Enums"]["data_type"];

export interface UnmatchedItem {
  id: string;
  source_value: string;
  source_system: { name: string };
  data_type: string;
  frequency: number;
  last_seen: string;
}

export interface MasterDataItem {
  id: string;
  name: string;
  description?: string;
}

export const useUnmatchedItems = (dataType: DataType) => {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['unmatched-items', dataType],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('unmapped_items')
        .select(`
          id,
          source_value,
          frequency,
          last_seen,
          source_system_id,
          source_systems(name)
        `)
        .eq('data_type', dataType);

      if (error) throw error;

      return (data || []).map(item => ({
        id: item.id,
        source_value: item.source_value,
        source_system: { name: item.source_systems?.name || 'Unknown' },
        data_type: dataType,
        frequency: item.frequency || 0,
        last_seen: new Date(item.last_seen).toISOString().split('T')[0]
      }));
    },
    enabled: !!user,
  });
};

export const useMasterData = (dataType: DataType) => {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['master-data', dataType],
    queryFn: async () => {
      let data;
      let error;

      if (dataType === 'menu-items') {
        const result = await supabase.from('master_menu_items').select('id, name, description');
        data = result.data;
        error = result.error;
      } else if (dataType === 'menu-categories') {
        const result = await supabase.from('master_menu_categories').select('id, name, description');
        data = result.data;
        error = result.error;
      } else if (dataType === 'day-parts') {
        const result = await supabase.from('master_day_parts').select('id, name, description');
        data = result.data;
        error = result.error;
      } else if (dataType === 'ingredients-supplies') {
        const result = await supabase.from('master_ingredients_supplies').select('id, name');
        data = Array.isArray(result.data)
          ? result.data.map(item => ({ id: item.id, name: item.name, description: null }))
          : [];
        error = result.error;
      } else if (dataType === 'locations') {
        const result = await supabase.from('master_locations').select('id, name');
        data = Array.isArray(result.data)
          ? result.data.map(item => ({ id: item.id, name: item.name, description: null }))
          : [];
        error = result.error;
      } else if (dataType === 'employees') {
        const result = await supabase.from('master_employees').select('id, name');
        data = Array.isArray(result.data)
          ? result.data.map(item => ({ id: item.id, name: item.name, description: null }))
          : [];
        error = result.error;
      } else if (dataType === 'restaurant-operators') {
        const result = await supabase.from('master_restaurant_operators').select('id, name');
        data = Array.isArray(result.data)
          ? result.data.map(item => ({ id: item.id, name: item.name, description: null }))
          : [];
        error = result.error;
      } else if (dataType === 'survey-questions') {
        const result = await supabase.from('master_survey_questions').select('id, question_text');
        data = Array.isArray(result.data)
          ? result.data.map(item => ({ id: item.id, name: item.question_text, description: null }))
          : [];
        error = result.error;
      } else {
        return [];
      }

      if (error) throw error;
      return data || [];
    },
    enabled: !!user,
  });
};

export const useCreateMatching = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (matching: {
      sourceId: string;
      sourceValue: string;
      masterValue: string;
      isNewMaster: boolean;
      dataType: string;
      masterTable?: string;
      masterId?: string;
    }) => {
      if (!user) throw new Error('User not authenticated');
      
      let masterId = matching.masterId;

      // If creating new master value, insert it first with owner_id
      if (matching.isNewMaster) {
        if (matching.dataType === 'menu-items') {
          const { data, error } = await supabase
            .from('master_menu_items')
            .insert({ name: matching.masterValue, owner_id: user.id })
            .select('id')
            .single();
          if (error) throw error;
          masterId = data.id;
        } else if (matching.dataType === 'menu-categories') {
          const { data, error } = await supabase
            .from('master_menu_categories')
            .insert({ name: matching.masterValue, owner_id: user.id })
            .select('id')
            .single();
          if (error) throw error;
          masterId = data.id;
        } else if (matching.dataType === 'day-parts') {
          const { data, error } = await supabase
            .from('master_day_parts')
            .insert({ name: matching.masterValue, owner_id: user.id })
            .select('id')
            .single();
          if (error) throw error;
          masterId = data.id;
        } else if (matching.dataType === 'ingredients-supplies') {
          const { data, error } = await supabase
            .from('master_ingredients_supplies')
            .insert({ name: matching.masterValue, owner_id: user.id })
            .select('id')
            .single();
          if (error) throw error;
          masterId = data.id;
        }
      }

      // Create the matching with mapped_by field
      const { error } = await supabase
        .from('data_mappings')
        .insert({
          source_value: matching.sourceValue,
          master_table: matching.masterTable || `master_${matching.dataType.replace('-', '_')}`,
          master_id: masterId!,
          data_type: matching.dataType as DataType,
          status: 'approved',
          mapped_by: user.id
        });

      if (error) throw error;

      // Remove from unmapped items
      await supabase
        .from('unmapped_items')
        .delete()
        .eq('id', matching.sourceId);

      return { success: true };
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['unmatched-items', variables.dataType] });
      queryClient.invalidateQueries({ queryKey: ['master-data', variables.dataType] });
    },
  });
};
