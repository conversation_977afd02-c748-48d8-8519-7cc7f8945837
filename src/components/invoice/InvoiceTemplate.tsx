import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Invoice } from '@/types/invoice';
import { Download, Mail, Printer } from 'lucide-react';

interface InvoiceTemplateProps {
  invoice: Invoice;
  onDownload?: () => void;
  onEmail?: () => void;
  onPrint?: () => void;
}

export function InvoiceTemplate({ invoice, onDownload, onEmail, onPrint }: InvoiceTemplateProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 mb-6 no-print">
        <Button variant="outline" onClick={onPrint} className="flex items-center">
          <Printer className="w-4 h-4 mr-2" />
          Print
        </Button>
        <Button variant="outline" onClick={onEmail} className="flex items-center">
          <Mail className="w-4 h-4 mr-2" />
          Email
        </Button>
        <Button onClick={onDownload} className="flex items-center bg-orange-600 hover:bg-orange-700">
          <Download className="w-4 h-4 mr-2" />
          Download PDF
        </Button>
      </div>

      <Card className="invoice-container">
        <CardContent className="p-8">
          {/* Header */}
          <div className="flex justify-between items-start mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{invoice.company.name}</h1>
              <div className="text-gray-600 mt-2">
                <p>{invoice.company.address.street}</p>
                <p>{invoice.company.address.city}, {invoice.company.address.state} {invoice.company.address.zipCode}</p>
                <p>{invoice.company.email}</p>
                <p>{invoice.company.phone}</p>
                {invoice.company.website && <p>{invoice.company.website}</p>}
              </div>
            </div>
            <div className="text-right">
              <h2 className="text-4xl font-bold text-orange-600 mb-2">INVOICE</h2>
              <div className="text-gray-600">
                <p><strong>Invoice #:</strong> {invoice.invoiceNumber}</p>
                <p><strong>Date:</strong> {formatDate(invoice.date)}</p>
                <p><strong>Due Date:</strong> {formatDate(invoice.dueDate)}</p>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Bill To */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Bill To:</h3>
            <div className="text-gray-600">
              <p className="font-medium">{invoice.client.name}</p>
              {invoice.client.company && <p>{invoice.client.company}</p>}
              <p>{invoice.client.address.street}</p>
              <p>{invoice.client.address.city}, {invoice.client.address.state} {invoice.client.address.zipCode}</p>
              <p>{invoice.client.email}</p>
            </div>
          </div>

          {/* Items Table */}
          <div className="mb-8">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b-2 border-gray-200">
                    <th className="text-left py-3 px-2 font-semibold text-gray-700">Description</th>
                    <th className="text-center py-3 px-2 font-semibold text-gray-700 w-20">Qty</th>
                    <th className="text-right py-3 px-2 font-semibold text-gray-700 w-24">Rate</th>
                    <th className="text-right py-3 px-2 font-semibold text-gray-700 w-24">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {invoice.items.map((item) => (
                    <tr key={item.id} className="border-b border-gray-100">
                      <td className="py-3 px-2 text-gray-700">{item.description}</td>
                      <td className="py-3 px-2 text-center text-gray-700">{item.quantity}</td>
                      <td className="py-3 px-2 text-right text-gray-700">{formatCurrency(item.rate)}</td>
                      <td className="py-3 px-2 text-right text-gray-700 font-medium">{formatCurrency(item.amount)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Totals */}
          <div className="flex justify-end mb-8">
            <div className="w-80">
              <div className="space-y-2">
                <div className="flex justify-between py-2">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="font-medium">{formatCurrency(invoice.subtotal)}</span>
                </div>
                {invoice.taxRate > 0 && (
                  <div className="flex justify-between py-2">
                    <span className="text-gray-600">Tax ({(invoice.taxRate * 100).toFixed(1)}%):</span>
                    <span className="font-medium">{formatCurrency(invoice.taxAmount)}</span>
                  </div>
                )}
                <Separator />
                <div className="flex justify-between py-3 text-lg font-bold">
                  <span>Total:</span>
                  <span className="text-orange-600">{formatCurrency(invoice.total)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {invoice.notes && (
            <div className="mb-6">
              <h4 className="font-semibold text-gray-900 mb-2">Notes:</h4>
              <p className="text-gray-600 text-sm leading-relaxed">{invoice.notes}</p>
            </div>
          )}

          {/* Footer */}
          <div className="text-center text-gray-500 text-sm border-t pt-6">
            <p>Thank you for your business!</p>
            <p>Payment is due within 30 days of invoice date.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
