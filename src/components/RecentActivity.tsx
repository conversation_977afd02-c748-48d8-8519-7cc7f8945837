
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

const activities = [
  {
    id: 1,
    user: "<PERSON>",
    action: "Created new conversation",
    time: "2 minutes ago",
    type: "create",
    initials: "<PERSON>",
  },
  {
    id: 2,
    user: "<PERSON>",
    action: "Updated user profile",
    time: "15 minutes ago",
    type: "update",
    initials: "<PERSON><PERSON>",
  },
  {
    id: 3,
    user: "<PERSON>",
    action: "Deleted conversation thread",
    time: "1 hour ago",
    type: "delete",
    initials: "ED",
  },
  {
    id: 4,
    user: "<PERSON>",
    action: "Logged into system",
    time: "2 hours ago",
    type: "login",
    initials: "AR",
  },
  {
    id: 5,
    user: "<PERSON>",
    action: "Exported user data",
    time: "3 hours ago",
    type: "export",
    initials: "J<PERSON>",
  },
];

const getTypeColor = (type: string) => {
  switch (type) {
    case "create": return "bg-green-100 text-green-800";
    case "update": return "bg-blue-100 text-blue-800";
    case "delete": return "bg-red-100 text-red-800";
    case "login": return "bg-gray-100 text-gray-800";
    case "export": return "bg-purple-100 text-purple-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

export function RecentActivity() {
  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-900">
          Recent Activity
        </CardTitle>
        <p className="text-sm text-gray-600">Latest user actions and system events</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <Avatar className="w-10 h-10">
                <AvatarFallback className="bg-blue-600 text-white text-sm font-medium">
                  {activity.initials}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900">
                  {activity.user}
                </p>
                <p className="text-sm text-gray-600">{activity.action}</p>
              </div>
              <div className="flex flex-col items-end space-y-1">
                <Badge className={`text-xs ${getTypeColor(activity.type)}`}>
                  {activity.type}
                </Badge>
                <span className="text-xs text-gray-500">{activity.time}</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
