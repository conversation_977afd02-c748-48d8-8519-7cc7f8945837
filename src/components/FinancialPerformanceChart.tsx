
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Legend } from "recharts";
import { useSalesTransactions } from "@/hooks/useRestaurantData";
import { Skeleton } from "@/components/ui/skeleton";
import { TrendingUp, DollarSign, Calendar, Target } from "lucide-react";
import { useMemo } from "react";

interface FinancialPerformanceChartProps {
  locationId?: string | null;
}

export const FinancialPerformanceChart = ({ locationId }: FinancialPerformanceChartProps) => {
  const { data: transactions, isLoading } = useSalesTransactions(locationId || undefined, 1000);

  // Process real transaction data into daily sales trends with period comparisons
  const chartData = useMemo(() => {
    if (!transactions || transactions.length === 0) {
      // Fallback mock data with period comparisons
      return [
        { 
          date: 'Jan 1', 
          currentPeriod: 4200, 
          previousPeriod: 3800, 
          previousYear: 3600,
          day: 'Mon'
        },
        { 
          date: 'Jan 2', 
          currentPeriod: 3800, 
          previousPeriod: 4100, 
          previousYear: 3400,
          day: 'Tue'
        },
        { 
          date: 'Jan 3', 
          currentPeriod: 5100, 
          previousPeriod: 4500, 
          previousYear: 4200,
          day: 'Wed'
        },
        { 
          date: 'Jan 4', 
          currentPeriod: 4700, 
          previousPeriod: 4300, 
          previousYear: 4000,
          day: 'Thu'
        },
        { 
          date: 'Jan 5', 
          currentPeriod: 6200, 
          previousPeriod: 5800, 
          previousYear: 5500,
          day: 'Fri'
        },
        { 
          date: 'Jan 6', 
          currentPeriod: 6800, 
          previousPeriod: 6200, 
          previousYear: 5900,
          day: 'Sat'
        },
        { 
          date: 'Jan 7', 
          currentPeriod: 5900, 
          previousPeriod: 5400, 
          previousYear: 5100,
          day: 'Sun'
        },
      ];
    }

    // Group transactions by date for last 14 days to compare current vs previous week
    const now = new Date();
    const last14Days = [];
    for (let i = 13; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      last14Days.push(date.toISOString().split('T')[0]);
    }

    const dailyData = transactions.reduce((acc, transaction) => {
      const date = new Date(transaction.transaction_date).toISOString().split('T')[0];
      
      if (!acc[date]) {
        acc[date] = 0;
      }
      
      acc[date] += Number(transaction.total_amount);
      return acc;
    }, {} as Record<string, number>);

    // Create comparison data for last 7 days vs previous 7 days
    const comparisonData = [];
    for (let i = 0; i < 7; i++) {
      const currentDate = last14Days[i + 7]; // Last 7 days
      const previousDate = last14Days[i]; // Previous 7 days
      const previousYearDate = new Date(currentDate);
      previousYearDate.setFullYear(previousYearDate.getFullYear() - 1);
      const previousYearDateStr = previousYearDate.toISOString().split('T')[0];

      const dayName = new Date(currentDate).toLocaleDateString('en-US', { weekday: 'short' });
      const monthDay = new Date(currentDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

      comparisonData.push({
        date: monthDay,
        day: dayName,
        currentPeriod: dailyData[currentDate] || 0,
        previousPeriod: dailyData[previousDate] || 0,
        previousYear: dailyData[previousYearDateStr] || 0,
      });
    }

    return comparisonData;
  }, [transactions]);

  // Calculate summary metrics
  const metrics = useMemo(() => {
    const currentTotal = chartData.reduce((sum, day) => sum + day.currentPeriod, 0);
    const previousTotal = chartData.reduce((sum, day) => sum + day.previousPeriod, 0);
    const previousYearTotal = chartData.reduce((sum, day) => sum + day.previousYear, 0);
    
    const weekOverWeekChange = previousTotal > 0 ? ((currentTotal - previousTotal) / previousTotal) * 100 : 0;
    const yearOverYearChange = previousYearTotal > 0 ? ((currentTotal - previousYearTotal) / previousYearTotal) * 100 : 0;

    return {
      currentTotal,
      previousTotal,
      previousYearTotal,
      weekOverWeekChange,
      yearOverYearChange,
      dailyAverage: currentTotal / 7
    };
  }, [chartData]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const viewLabel = locationId ? "Location-specific data" : "Portfolio overview";

  return (
    <div className="space-y-6">
      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-2 border-blue-200 bg-gradient-to-br from-white to-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Target className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-700">This Week</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(metrics.currentTotal)}
            </div>
            <div className="text-xs text-blue-600">
              Daily avg: {formatCurrency(metrics.dailyAverage)}
            </div>
          </CardContent>
        </Card>

        <Card className="border-2 border-gray-200 bg-gradient-to-br from-white to-gray-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">Last Week</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(metrics.previousTotal)}
            </div>
            <div className={`text-xs font-medium ${metrics.weekOverWeekChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {metrics.weekOverWeekChange >= 0 ? '+' : ''}{metrics.weekOverWeekChange.toFixed(1)}% vs last week
            </div>
          </CardContent>
        </Card>

        <Card className="border-2 border-purple-200 bg-gradient-to-br from-white to-purple-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-gray-700">Last Year</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(metrics.previousYearTotal)}
            </div>
            <div className={`text-xs font-medium ${metrics.yearOverYearChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {metrics.yearOverYearChange >= 0 ? '+' : ''}{metrics.yearOverYearChange.toFixed(1)}% YoY
            </div>
          </CardContent>
        </Card>

        <Card className="border-2 border-green-200 bg-gradient-to-br from-white to-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700">Performance</span>
            </div>
            <div className={`text-2xl font-bold ${metrics.weekOverWeekChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {metrics.weekOverWeekChange >= 0 ? '↗' : '↘'} {Math.abs(metrics.weekOverWeekChange).toFixed(1)}%
            </div>
            <div className="text-xs text-green-600">
              Week-over-week trend
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Daily Sales Trend Chart */}
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50">
        <CardHeader>
          <div className="flex items-center gap-2">
            <TrendingUp className="w-6 h-6 text-orange-600" />
            <CardTitle className="text-xl">Daily Sales Trend Comparison</CardTitle>
          </div>
          <CardDescription>
            7-day sales comparison: Current week vs Previous week vs Same period last year
            <span className="block text-xs text-gray-500 mt-1">{viewLabel}</span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-80 w-full" />
          ) : (
            <ResponsiveContainer width="100%" height={350}>
              <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f97316" opacity={0.2} />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 12, fill: '#7c2d12' }}
                />
                <YAxis 
                  tick={{ fontSize: 12, fill: '#7c2d12' }}
                  tickFormatter={(value) => `$${(value/1000).toFixed(0)}K`}
                />
                <Tooltip 
                  formatter={(value: number, name: string) => {
                    const label = name === 'currentPeriod' ? 'This Week' : 
                                 name === 'previousPeriod' ? 'Last Week' : 'Last Year';
                    return [formatCurrency(value), label];
                  }}
                  labelFormatter={(label, payload) => {
                    if (payload && payload[0]) {
                      return `${payload[0].payload.day}, ${label}`;
                    }
                    return label;
                  }}
                  contentStyle={{ 
                    backgroundColor: '#fff7ed', 
                    border: '2px solid #fed7aa',
                    borderRadius: '8px'
                  }}
                />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="currentPeriod" 
                  stroke="#ea580c" 
                  strokeWidth={4}
                  dot={{ fill: '#ea580c', strokeWidth: 2, r: 6 }}
                  name="This Week"
                />
                <Line 
                  type="monotone" 
                  dataKey="previousPeriod" 
                  stroke="#6b7280" 
                  strokeWidth={3}
                  strokeDasharray="8 4"
                  dot={{ fill: '#6b7280', strokeWidth: 2, r: 4 }}
                  name="Last Week"
                />
                <Line 
                  type="monotone" 
                  dataKey="previousYear" 
                  stroke="#8b5cf6" 
                  strokeWidth={2}
                  strokeDasharray="4 4"
                  dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 3 }}
                  name="Last Year"
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
