
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Clock } from "lucide-react";
import { Notification } from "@/types/notification";

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  notifications: Notification[];
  onCategorizeItem: (notificationId: string) => void;
  onMarkAsSeen: (notificationId: string) => void;
}

export function NotificationModal({
  isOpen,
  onClose,
  notifications,
  onCategorizeItem,
  onMarkAsSeen
}: NotificationModalProps) {
  const unreadNotifications = notifications.filter(n => !n.isRead);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-gray-900 text-white border-gray-700">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-lg font-semibold">
            Notifications
            {unreadNotifications.length > 0 && (
              <Badge className="ml-2 bg-red-500 text-white">
                {unreadNotifications.length}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {unreadNotifications.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <Clock className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No new notifications</p>
            </div>
          ) : (
            unreadNotifications.map((notification) => (
              <div key={notification.id} className="bg-gray-800 rounded-lg p-4 space-y-3">
                <div className="flex items-start space-x-3">
                  {notification.itemImage && (
                    <div className="w-12 h-12 bg-gray-700 rounded-lg flex-shrink-0 overflow-hidden">
                      <img 
                        src={notification.itemImage} 
                        alt={notification.itemName}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge className="bg-yellow-500 text-black text-xs">
                        {notification.title}
                      </Badge>
                    </div>
                    {notification.itemName && (
                      <h4 className="font-medium text-white mb-1">
                        {notification.itemName}
                      </h4>
                    )}
                    <p className="text-sm text-gray-300">
                      {notification.description}
                    </p>
                  </div>
                </div>
                
                <div className="flex space-x-2 pt-2">
                  <Button
                    onClick={() => onCategorizeItem(notification.id)}
                    className="bg-green-600 hover:bg-green-700 text-white text-sm px-3 py-1.5 h-auto"
                  >
                    Categorize Item
                  </Button>
                  <Button
                    onClick={() => onMarkAsSeen(notification.id)}
                    variant="outline"
                    className="border-gray-600 text-gray-300 hover:bg-gray-700 text-sm px-3 py-1.5 h-auto"
                  >
                    Mark As Seen
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
