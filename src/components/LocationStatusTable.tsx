
import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useRestaurantLocations, useDataIngestionLogs } from "@/hooks/useRestaurantData";
import { Skeleton } from "@/components/ui/skeleton";

export const LocationStatusTable = () => {
  const { data: locations, isLoading: locationsLoading } = useRestaurantLocations();
  const { data: logs } = useDataIngestionLogs();

  const getStatusBadge = (status: string) => {
    const variants = {
      active: "default",
      inactive: "secondary",
      error: "destructive"
    } as const;
    
    return <Badge variant={variants[status as keyof typeof variants] || "secondary"}>{status}</Badge>;
  };

  const getLastSync = (locationId: string) => {
    const locationLogs = logs?.filter(log => log.location_id === locationId);
    if (!locationLogs || locationLogs.length === 0) return 'Never';
    
    const lastLog = locationLogs[0];
    return new Date(lastLog.started_at).toLocaleString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Location Status</CardTitle>
        <CardDescription>
          Monitor the health and sync status of your restaurant locations
        </CardDescription>
      </CardHeader>
      <CardContent>
        {locationsLoading ? (
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        ) : !locations || locations.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p className="text-lg font-medium">No locations found</p>
            <p className="text-sm">Add restaurant locations to start monitoring</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Location</TableHead>
                <TableHead>Address</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Manager</TableHead>
                <TableHead>Last Sync</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {locations.map((location) => (
                <TableRow key={location.id}>
                  <TableCell className="font-medium">{location.name}</TableCell>
                  <TableCell>
                    {location.address ? (
                      <div className="text-sm">
                        <div>{location.address}</div>
                        <div className="text-gray-500">
                          {location.city}, {location.state} {location.zip_code}
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">No address</span>
                    )}
                  </TableCell>
                  <TableCell>{getStatusBadge(location.status)}</TableCell>
                  <TableCell>{location.manager_name || '-'}</TableCell>
                  <TableCell className="text-sm text-gray-500">
                    {getLastSync(location.id)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};
