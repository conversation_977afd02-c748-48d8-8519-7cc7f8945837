
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus } from "lucide-react";
import { TeamMember } from "@/types/teamMember";

interface TeamMemberFormProps {
  onAddMember: (member: TeamMember) => void;
  loading: boolean;
}

export function TeamMemberForm({ onAddMember, loading }: TeamMemberFormProps) {
  const [currentMember, setCurrentMember] = useState<TeamMember>({
    email: '',
    role: 'Business Admin'
  });

  const handleSubmit = () => {
    onAddMember(currentMember);
    setCurrentMember({ email: '', role: 'Business Admin' });
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-2 space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={currentMember.email}
            onChange={(e) => setCurrentMember({ ...currentMember, email: e.target.value })}
            disabled={loading}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="role">Role</Label>
          <Select 
            value={currentMember.role} 
            onValueChange={(value: any) => setCurrentMember({ ...currentMember, role: value })}
            disabled={loading}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Business Admin">Business Admin</SelectItem>
              <SelectItem value="Data Admin">Data Admin</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Button 
        type="button" 
        variant="outline" 
        onClick={handleSubmit}
        className="w-full"
        disabled={loading}
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Team Member
      </Button>
    </div>
  );
}
