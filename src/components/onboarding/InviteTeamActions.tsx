
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface InviteTeamActionsProps {
  onSkip: () => void;
  onSendInvitations: () => void;
  loading: boolean;
  teamMembersCount: number;
}

export function InviteTeamActions({ onSkip, onSendInvitations, loading, teamMembersCount }: InviteTeamActionsProps) {
  return (
    <div className="flex space-x-3 pt-4">
      <Button 
        variant="outline" 
        onClick={onSkip}
        className="flex-1"
        disabled={loading}
      >
        Skip for Now
      </Button>
      <Button 
        onClick={onSendInvitations}
        className="flex-1"
        disabled={loading}
      >
        {loading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Sending Invitations...
          </>
        ) : teamMembersCount > 0 ? (
          `Send ${teamMembersCount} Invitation${teamMembersCount > 1 ? 's' : ''}`
        ) : (
          'Continue'
        )}
      </Button>
    </div>
  );
}
