
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { TeamMember } from "@/types/teamMember";

interface TeamMembersListProps {
  teamMembers: TeamMember[];
  onRemoveMember: (index: number) => void;
  loading: boolean;
}

export function TeamMembersList({ teamMembers, onRemoveMember, loading }: TeamMembersListProps) {
  if (teamMembers.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3">
      <Label>Team Members to Invite</Label>
      <div className="space-y-2">
        {teamMembers.map((member, index) => (
          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <span className="font-medium">{member.email}</span>
              <Badge variant="secondary">{member.role}</Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onRemoveMember(index)}
              disabled={loading}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}
