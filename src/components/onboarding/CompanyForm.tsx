
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export interface CompanyFormData {
  name: string;
  industry: 'Restaurant' | 'Hospitality' | 'Food Service' | 'Other' | '';
}

interface CompanyFormProps {
  formData: CompanyFormData;
  onFormDataChange: (data: CompanyFormData) => void;
  loading: boolean;
}

export function CompanyForm({ formData, onFormDataChange, loading }: CompanyFormProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="companyName">Company Name</Label>
        <Input
          id="companyName"
          placeholder="Your Company Name"
          value={formData.name}
          onChange={(e) => onFormDataChange({ ...formData, name: e.target.value })}
          disabled={loading}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="industry">Industry</Label>
        <Select 
          value={formData.industry} 
          onValueChange={(value: any) => onFormDataChange({ ...formData, industry: value })}
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select your industry" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Restaurant">Restaurant</SelectItem>
            <SelectItem value="Hospitality">Hospitality</SelectItem>
            <SelectItem value="Food Service">Food Service</SelectItem>
            <SelectItem value="Other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
