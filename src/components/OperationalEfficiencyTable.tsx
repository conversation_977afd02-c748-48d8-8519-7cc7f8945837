
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useRestaurantLocations } from "@/hooks/useRestaurantData";
import { Skeleton } from "@/components/ui/skeleton";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

export const OperationalEfficiencyTable = () => {
  const { data: locations, isLoading: locationsLoading } = useRestaurantLocations();

  // Mock operational data for each location - in real implementation, this would come from operational analytics
  const mockOperationalData = [
    {
      locationId: '1',
      avgTicketTime: 12.5,
      customerSatisfaction: 4.2,
      turnoverRate: 8.5,
      foodWastePercentage: 3.2,
      salesPerSqFt: 485,
      laborEfficiency: 92,
      trend: 'up'
    },
    {
      locationId: '2',
      avgTicketTime: 15.2,
      customerSatisfaction: 3.8,
      turnoverRate: 12.1,
      foodWastePercentage: 4.8,
      salesPerSqFt: 425,
      laborEfficiency: 78,
      trend: 'down'
    },
    {
      locationId: '3',
      avgTicketTime: 11.8,
      customerSatisfaction: 4.5,
      turnoverRate: 6.2,
      foodWastePercentage: 2.8,
      salesPerSqFt: 520,
      laborEfficiency: 95,
      trend: 'up'
    }
  ];

  const getPerformanceBadge = (score: number, type: 'efficiency' | 'satisfaction' | 'waste') => {
    let threshold1, threshold2, variant1, variant2, variant3;
    
    switch (type) {
      case 'efficiency':
        threshold1 = 90; threshold2 = 75;
        variant1 = 'default'; variant2 = 'secondary'; variant3 = 'destructive';
        break;
      case 'satisfaction':
        threshold1 = 4.0; threshold2 = 3.5;
        variant1 = 'default'; variant2 = 'secondary'; variant3 = 'destructive';
        break;
      case 'waste':
        threshold1 = 3.0; threshold2 = 5.0;
        variant1 = 'default'; variant2 = 'secondary'; variant3 = 'destructive';
        return score <= threshold1 ? variant1 : score <= threshold2 ? variant2 : variant3;
      default:
        variant1 = 'secondary';
    }
    
    return score >= threshold1 ? variant1 : score >= threshold2 ? variant2 : variant3;
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <Minus className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-orange-600" />
          Operational Efficiency Dashboard
        </CardTitle>
        <CardDescription>
          Key performance indicators for operational excellence across all locations
        </CardDescription>
      </CardHeader>
      <CardContent>
        {locationsLoading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        ) : !locations || locations.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p className="text-lg font-medium">No operational data available</p>
            <p className="text-sm">Connect your POS systems to see performance metrics</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Location</TableHead>
                  <TableHead>Avg Ticket Time</TableHead>
                  <TableHead>Customer Rating</TableHead>
                  <TableHead>Staff Turnover</TableHead>
                  <TableHead>Food Waste %</TableHead>
                  <TableHead>Sales/Sq Ft</TableHead>
                  <TableHead>Labor Efficiency</TableHead>
                  <TableHead>Trend</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {locations.slice(0, 3).map((location, index) => {
                  const operationalData = mockOperationalData[index] || mockOperationalData[0];
                  return (
                    <TableRow key={location.id}>
                      <TableCell className="font-medium">
                        <div>
                          <div className="font-semibold">{location.name}</div>
                          <div className="text-sm text-gray-500">{location.city}, {location.state}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{operationalData.avgTicketTime} min</span>
                          <Badge variant={operationalData.avgTicketTime <= 12 ? 'default' : 'destructive'} className="text-xs">
                            {operationalData.avgTicketTime <= 12 ? 'Fast' : 'Slow'}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{operationalData.customerSatisfaction}/5.0</span>
                          <Badge variant={getPerformanceBadge(operationalData.customerSatisfaction, 'satisfaction')} className="text-xs">
                            {operationalData.customerSatisfaction >= 4.0 ? 'Excellent' : operationalData.customerSatisfaction >= 3.5 ? 'Good' : 'Needs Improvement'}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{operationalData.turnoverRate}%</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{operationalData.foodWastePercentage}%</span>
                          <Badge variant={getPerformanceBadge(operationalData.foodWastePercentage, 'waste')} className="text-xs">
                            {operationalData.foodWastePercentage <= 3.0 ? 'Excellent' : operationalData.foodWastePercentage <= 5.0 ? 'Good' : 'High'}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">${operationalData.salesPerSqFt}</span>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{operationalData.laborEfficiency}%</span>
                            <Badge variant={getPerformanceBadge(operationalData.laborEfficiency, 'efficiency')} className="text-xs">
                              {operationalData.laborEfficiency >= 90 ? 'Excellent' : operationalData.laborEfficiency >= 75 ? 'Good' : 'Poor'}
                            </Badge>
                          </div>
                          <Progress value={operationalData.laborEfficiency} className="h-2" />
                        </div>
                      </TableCell>
                      <TableCell>
                        {getTrendIcon(operationalData.trend)}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
