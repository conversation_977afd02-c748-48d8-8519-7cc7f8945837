
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { useCreateDataIngestionLog, useUpdateDataIngestionLog, useCreateSalesTransactions } from '@/hooks/useRestaurantData';

interface FileUploadModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const FileUploadModal = ({ open, onOpenChange }: FileUploadModalProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<{
    processed: number;
    failed: number;
    errors: string[];
  } | null>(null);

  const { toast } = useToast();
  const { user } = useAuth();
  const createLog = useCreateDataIngestionLog();
  const updateLog = useUpdateDataIngestionLog();
  const createTransactions = useCreateSalesTransactions();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile && selectedFile.type === 'text/csv') {
      setFile(selectedFile);
      setResults(null);
    } else {
      toast({
        title: "Invalid File",
        description: "Please select a CSV file.",
        variant: "destructive"
      });
    }
  };

  const parseCSV = (csvText: string) => {
    const lines = csvText.split('\n');
    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    
    console.log('CSV Headers:', headers);
    
    const transactions = [];
    const errors = [];

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      try {
        const values = line.split(',');
        if (values.length < headers.length) continue;

        const transaction: any = {
          transaction_date: new Date().toISOString(),
          total_amount: 0,
          source_system: 'csv_upload'
        };

        headers.forEach((header, index) => {
          const value = values[index]?.trim();
          
          switch (header) {
            case 'date':
            case 'transaction_date':
              if (value) {
                transaction.transaction_date = new Date(value).toISOString();
              }
              break;
            case 'amount':
            case 'total':
            case 'total_amount':
              transaction.total_amount = parseFloat(value) || 0;
              break;
            case 'tax':
            case 'tax_amount':
              transaction.tax_amount = parseFloat(value) || 0;
              break;
            case 'tip':
            case 'tip_amount':
              transaction.tip_amount = parseFloat(value) || 0;
              break;
            case 'payment_method':
            case 'payment':
              transaction.payment_method = value;
              break;
            case 'order_type':
            case 'type':
              transaction.order_type = value;
              break;
            case 'customer_count':
            case 'customers':
              transaction.customer_count = parseInt(value) || 1;
              break;
            case 'pos_id':
            case 'transaction_id':
              transaction.pos_transaction_id = value;
              break;
          }
        });

        if (transaction.total_amount > 0) {
          transactions.push(transaction);
        }
      } catch (error) {
        errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Parse error'}`);
      }
    }

    return { transactions, errors };
  };

  const handleUpload = async () => {
    if (!file || !user) return;

    setUploading(true);
    setProgress(0);

    try {
      // Create ingestion log
      const log = await createLog.mutateAsync({
        source_type: 'csv',
        source_name: file.name,
        status: 'processing',
        created_by: user.id
      });

      setProgress(25);

      // Read and parse CSV
      const csvText = await file.text();
      const { transactions, errors } = parseCSV(csvText);

      setProgress(50);

      console.log('Parsed transactions:', transactions.length);
      console.log('Parse errors:', errors.length);

      if (transactions.length === 0) {
        throw new Error('No valid transactions found in CSV file');
      }

      // Insert transactions in batches
      await createTransactions.mutateAsync(transactions);

      setProgress(100);

      // Update log with results
      await updateLog.mutateAsync({
        id: log.id,
        updates: {
          status: 'completed',
          records_processed: transactions.length,
          records_failed: errors.length,
          completed_at: new Date().toISOString(),
          error_details: errors.length > 0 ? { errors } : null
        }
      });

      setResults({
        processed: transactions.length,
        failed: errors.length,
        errors: errors.slice(0, 5) // Show first 5 errors
      });

      toast({
        title: "Upload Successful",
        description: `Processed ${transactions.length} transactions successfully.`,
      });

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Failed to upload file",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
    }
  };

  const handleClose = () => {
    setFile(null);
    setResults(null);
    setProgress(0);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Upload Sales Data
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {!results && (
            <>
              <div>
                <Label htmlFor="csv-file">CSV File</Label>
                <Input
                  id="csv-file"
                  type="file"
                  accept=".csv"
                  onChange={handleFileChange}
                  disabled={uploading}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Expected columns: date, amount, payment_method, order_type
                </p>
              </div>

              {file && (
                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded">
                  <FileText className="w-4 h-4" />
                  <span className="text-sm">{file.name}</span>
                </div>
              )}

              {uploading && (
                <div className="space-y-2">
                  <Progress value={progress} />
                  <p className="text-sm text-center">Processing file...</p>
                </div>
              )}
            </>
          )}

          {results && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">Upload Complete</span>
              </div>
              
              <div className="space-y-2 text-sm">
                <p>✅ {results.processed} transactions processed</p>
                {results.failed > 0 && (
                  <p className="text-orange-600">⚠️ {results.failed} rows had errors</p>
                )}
              </div>

              {results.errors.length > 0 && (
                <div className="max-h-32 overflow-y-auto">
                  <p className="text-xs text-gray-600 mb-1">Sample errors:</p>
                  {results.errors.map((error, index) => (
                    <p key={index} className="text-xs text-red-600">{error}</p>
                  ))}
                </div>
              )}
            </div>
          )}

          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={handleClose} className="flex-1">
              {results ? 'Close' : 'Cancel'}
            </Button>
            {!results && (
              <Button 
                onClick={handleUpload} 
                disabled={!file || uploading}
                className="flex-1"
              >
                {uploading ? 'Processing...' : 'Upload'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
