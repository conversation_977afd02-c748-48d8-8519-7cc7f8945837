
import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertTriangle, Clock, CheckCircle, XCircle, Eye, MessageSquare, Pause } from "lucide-react";
import { AlertFilters } from "@/pages/Alerts";
import { useToast } from "@/hooks/use-toast";
import { useAlerts, useUpdateAlert, useBulkUpdateAlerts } from "@/hooks/useAlerts";

const getSeverityBadge = (severity: string) => {
  switch (severity) {
    case "critical":
      return <Badge className="bg-red-100 text-red-800">Critical</Badge>;
    case "high":
      return <Badge className="bg-orange-100 text-orange-800">High</Badge>;
    case "medium":
      return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
    case "low":
      return <Badge className="bg-blue-100 text-blue-800">Low</Badge>;
    default:
      return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case "active":
      return <XCircle className="w-4 h-4 text-red-500" />;
    case "acknowledged":
      return <Clock className="w-4 h-4 text-yellow-500" />;
    case "resolved":
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    default:
      return <AlertTriangle className="w-4 h-4 text-gray-500" />;
  }
};

const getLocationName = (alert: any) => {
  if (alert.metadata?.source_name) return alert.metadata.source_name;
  if (alert.metadata?.integration_name) return alert.metadata.integration_name;
  if (alert.metadata?.location_name) return alert.metadata.location_name;
  return "All Locations";
};

const getTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 60) return `${diffMins} min ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
};

interface AlertsTableProps {
  filters: AlertFilters;
  selectedAlerts: string[];
  onSelectionChange: (selected: string[]) => void;
}

export function AlertsTable({ filters, selectedAlerts, onSelectionChange }: AlertsTableProps) {
  const { toast } = useToast();
  const { data: alerts = [], isLoading, error } = useAlerts();
  const updateAlert = useUpdateAlert();
  const bulkUpdateAlerts = useBulkUpdateAlerts();

  const filteredAlerts = useMemo(() => {
    return alerts.filter(alert => {
      const severityMatch = filters.severity.length === 0 || filters.severity.includes(alert.severity);
      const statusMatch = filters.status.length === 0 || filters.status.includes(alert.status);
      const locationName = getLocationName(alert);
      const locationMatch = filters.locations.length === 0 || filters.locations.includes(locationName);
      
      return severityMatch && statusMatch && locationMatch;
    });
  }, [alerts, filters]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(filteredAlerts.map(alert => alert.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectAlert = (alertId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedAlerts, alertId]);
    } else {
      onSelectionChange(selectedAlerts.filter(id => id !== alertId));
    }
  };

  const handleMarkAllRead = async () => {
    if (selectedAlerts.length === 0) {
      toast({
        title: "No alerts selected",
        description: "Please select alerts to mark as read."
      });
      return;
    }

    try {
      await bulkUpdateAlerts.mutateAsync({
        ids: selectedAlerts,
        updates: { status: 'acknowledged', acknowledged_at: new Date().toISOString() }
      });

      toast({
        title: "Alerts marked as read",
        description: `${selectedAlerts.length} alert(s) have been marked as read.`
      });
      onSelectionChange([]);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update alerts. Changes applied locally.",
        variant: "destructive"
      });
    }
  };

  const handleBulkActions = async () => {
    if (selectedAlerts.length === 0) {
      toast({
        title: "No alerts selected",
        description: "Please select alerts to perform bulk actions."
      });
      return;
    }

    try {
      await bulkUpdateAlerts.mutateAsync({
        ids: selectedAlerts,
        updates: { status: 'resolved', resolved_at: new Date().toISOString() }
      });

      toast({
        title: "Bulk action performed",
        description: `Action performed on ${selectedAlerts.length} alert(s).`
      });
      onSelectionChange([]);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to perform bulk action. Changes applied locally.",
        variant: "destructive"
      });
    }
  };

  const handleAlertAction = async (action: string, alertId: string) => {
    const alert = filteredAlerts.find(a => a.id === alertId);
    
    if (action === 'Pause') {
      try {
        await updateAlert.mutateAsync({
          id: alertId,
          updates: { status: 'acknowledged', acknowledged_at: new Date().toISOString() }
        });
      } catch (error) {
        console.warn('Failed to update alert in database:', error);
      }
    }

    toast({
      title: `${action} action`,
      description: `${action} performed on "${alert?.title}"`
    });
  };

  const isAllSelected = filteredAlerts.length > 0 && selectedAlerts.length === filteredAlerts.length;

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="text-gray-500">Loading alerts...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="text-red-500">Error loading alerts. Using demonstration data.</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between bulk-actions">
        <CardTitle className="text-lg font-semibold">
          Alert Details {filteredAlerts.length !== alerts.length && `(${filteredAlerts.length} of ${alerts.length})`}
        </CardTitle>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleMarkAllRead}>
            Mark All Read
          </Button>
          <Button variant="outline" size="sm" onClick={handleBulkActions}>
            Bulk Actions
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox 
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Alert</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Severity</TableHead>
              <TableHead className="status-icons">Status</TableHead>
              <TableHead>Time</TableHead>
              <TableHead className="alert-actions">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAlerts.map((alert) => (
              <TableRow key={alert.id} className="hover:bg-gray-50">
                <TableCell>
                  <Checkbox 
                    checked={selectedAlerts.includes(alert.id)}
                    onCheckedChange={(checked) => handleSelectAlert(alert.id, checked as boolean)}
                  />
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium text-gray-900">{alert.title}</div>
                    <div className="text-sm text-gray-600">{alert.description}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">{getLocationName(alert)}</div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">{alert.category}</div>
                </TableCell>
                <TableCell>
                  {getSeverityBadge(alert.severity)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(alert.status)}
                    <span className="text-sm capitalize">{alert.status}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm text-gray-600">{getTimeAgo(alert.created_at)}</div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-1">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleAlertAction("View", alert.id)}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleAlertAction("Comment", alert.id)}
                    >
                      <MessageSquare className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleAlertAction("Pause", alert.id)}
                    >
                      <Pause className="w-4 h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {filteredAlerts.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No alerts match the current filters.
          </div>
        )}
        
        <div className="mt-6">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious href="#" />
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#" isActive>1</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">2</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">3</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationNext href="#" />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </CardContent>
    </Card>
  );
}
