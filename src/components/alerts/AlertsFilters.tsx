
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Filter, X } from "lucide-react";
import { AlertFilters } from "@/pages/Alerts";
import { useAlerts } from "@/hooks/useAlerts";
import { useMemo } from "react";

interface AlertsFiltersProps {
  filters: AlertFilters;
  onFilterChange: (category: keyof AlertFilters, value: string, checked: boolean) => void;
  onClearAll: () => void;
}

export function AlertsFilters({ filters, onFilterChange, onClearAll }: AlertsFiltersProps) {
  const { data: alerts = [] } = useAlerts();

  const filterCounts = useMemo(() => {
    const severityCounts = { critical: 0, high: 0, medium: 0, low: 0 };
    const statusCounts = { active: 0, acknowledged: 0, resolved: 0 };
    const locationCounts: Record<string, number> = {};

    alerts.forEach(alert => {
      // Count by severity
      if (alert.severity in severityCounts) {
        severityCounts[alert.severity as keyof typeof severityCounts]++;
      }

      // Count by status
      if (alert.status in statusCounts) {
        statusCounts[alert.status as keyof typeof statusCounts]++;
      }

      // Count by location
      const locationName = alert.metadata?.source_name || 
                          alert.metadata?.integration_name || 
                          alert.metadata?.location_name || 
                          "All Locations";
      locationCounts[locationName] = (locationCounts[locationName] || 0) + 1;
    });

    return { severityCounts, statusCounts, locationCounts };
  }, [alerts]);

  const severityFilters = [
    { label: "Critical", count: filterCounts.severityCounts.critical, color: "bg-red-100 text-red-800" },
    { label: "High", count: filterCounts.severityCounts.high, color: "bg-orange-100 text-orange-800" },
    { label: "Medium", count: filterCounts.severityCounts.medium, color: "bg-yellow-100 text-yellow-800" },
    { label: "Low", count: filterCounts.severityCounts.low, color: "bg-blue-100 text-blue-800" }
  ];

  const statusFilters = [
    { label: "Active", count: filterCounts.statusCounts.active },
    { label: "Acknowledged", count: filterCounts.statusCounts.acknowledged },
    { label: "Resolved", count: filterCounts.statusCounts.resolved }
  ];

  const locationFilters = Object.entries(filterCounts.locationCounts)
    .map(([label, count]) => ({ label, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 8); // Show top 8 locations

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center">
          <Filter className="w-5 h-5 mr-2" />
          Filters
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="severity-filters">
          <h4 className="font-medium text-gray-900 mb-3">Severity</h4>
          <div className="space-y-2">
            {severityFilters.map((filter) => (
              <div key={filter.label} className="flex items-center justify-between">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <Checkbox 
                    checked={filters.severity.includes(filter.label.toLowerCase())}
                    onCheckedChange={(checked) => 
                      onFilterChange('severity', filter.label.toLowerCase(), checked as boolean)
                    }
                  />
                  <span className="text-sm">{filter.label}</span>
                </label>
                <Badge className={filter.color}>{filter.count}</Badge>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        <div>
          <h4 className="font-medium text-gray-900 mb-3">Status</h4>
          <div className="space-y-2">
            {statusFilters.map((filter) => (
              <div key={filter.label} className="flex items-center justify-between">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <Checkbox 
                    checked={filters.status.includes(filter.label.toLowerCase())}
                    onCheckedChange={(checked) => 
                      onFilterChange('status', filter.label.toLowerCase(), checked as boolean)
                    }
                  />
                  <span className="text-sm">{filter.label}</span>
                </label>
                <span className="text-xs text-gray-500">{filter.count}</span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        <div>
          <h4 className="font-medium text-gray-900 mb-3">Locations</h4>
          <div className="space-y-2">
            {locationFilters.map((filter) => (
              <div key={filter.label} className="flex items-center justify-between">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <Checkbox 
                    checked={filters.locations.includes(filter.label)}
                    onCheckedChange={(checked) => 
                      onFilterChange('locations', filter.label, checked as boolean)
                    }
                  />
                  <span className="text-xs">{filter.label}</span>
                </label>
                <span className="text-xs text-gray-500">{filter.count}</span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        <Button variant="outline" size="sm" className="w-full" onClick={onClearAll}>
          <X className="w-4 h-4 mr-2" />
          Clear All
        </Button>
      </CardContent>
    </Card>
  );
}
