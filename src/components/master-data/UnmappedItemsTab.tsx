
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Target, Search, Calendar } from "lucide-react";
import { Input } from "@/components/ui/input";
import { MatchingModal } from "./MatchingModal";

const unmatchedItems = [
  { id: 1, sourceValue: "Unknown Item 2024/04/03", frequency: 156, source: "System Import", lastSeen: "2024-06-03", category: "Mixed" },
  { id: 2, sourceValue: "Misc Revenue Entry", frequency: 89, source: "Manual Entry", lastSeen: "2024-06-03", category: "Revenue" },
  { id: 3, sourceValue: "Uncat Product XYZ", frequency: 67, source: "Legacy System", lastSeen: "2024-06-02", category: "Product" },
  { id: 4, sourceValue: "Staff Entry 123", frequency: 45, source: "Time System", lastSeen: "2024-06-03", category: "Employee" },
];

const masterCategories = [
  "Menu Items",
  "Food Sales",
  "Locations", 
  "Menu & Products",
  "Menu Categories",
  "Day Parts",
  "Survey Questions",
  "Restaurant Operators",
  "Ingredients & Supplies",
  "Employees",
  "Date Formats",
];

export function UnmappedItemsTab() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const filteredItems = unmatchedItems.filter(item =>
    item.sourceValue.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleMatchItem = (item) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const handleSaveMatching = (matching) => {
    console.log("Saving unmatched item matching:", matching);
    setIsModalOpen(false);
    setSelectedItem(null);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search unmatched items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <Badge variant="secondary" className="bg-red-100 text-red-800">
            {filteredItems.length} unmatched items
          </Badge>
        </div>
        <div className="flex items-center text-sm text-gray-500">
          <Calendar className="w-4 h-4 mr-1" />
          Last updated: 2024/04/03
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Source Value</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Source System</TableHead>
              <TableHead>Frequency</TableHead>
              <TableHead>Last Seen</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sourceValue}</TableCell>
                <TableCell>
                  <Badge variant="secondary">{item.category}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{item.source}</Badge>
                </TableCell>
                <TableCell>{item.frequency}</TableCell>
                <TableCell>{item.lastSeen}</TableCell>
                <TableCell>
                  <Button
                    size="sm"
                    onClick={() => handleMatchItem(item)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Target className="w-4 h-4 mr-1" />
                    Match
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <MatchingModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        item={selectedItem}
        masterOptions={masterCategories}
        onSave={handleSaveMatching}
        type="Data Category"
      />
    </div>
  );
}
