
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

interface SourceDataCardProps {
  item: {
    sourceValue: string;
    source: string;
    frequency: number;
    lastSeen: string;
  };
}

export function SourceDataCard({ item }: SourceDataCardProps) {
  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
        <span>📋</span>
        Source Data Found
      </Label>
      <div className="p-4 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg border-2 border-orange-200">
        <div className="flex items-center justify-between mb-2">
          <span className="font-medium text-gray-800">{item.sourceValue}</span>
          <Badge variant="outline" className="border-orange-300 text-orange-700 bg-white">
            {item.source}
          </Badge>
        </div>
        <div className="text-sm text-gray-600">
          Frequency: {item.frequency} times | Last seen: {item.lastSeen}
        </div>
      </div>
    </div>
  );
}
