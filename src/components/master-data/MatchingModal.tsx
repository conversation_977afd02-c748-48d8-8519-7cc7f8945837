
import { useState } from "react";
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Target, Plus, Check, ArrowRight, Building2, TrendingUp } from "lucide-react";

interface MatchingModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: any;
  masterOptions: string[];
  onSave: (matching: {
    sourceId: string;
    sourceValue: string;
    masterValue: string;
    isNewMaster: boolean;
  }) => void;
  type: string;
}

export function MatchingModal({ isOpen, onClose, item, masterOptions, onSave, type }: MatchingModalProps) {
  const [selectedMaster, setSelectedMaster] = useState("");
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [newMasterValue, setNewMasterValue] = useState("");

  const handleSave = () => {
    const masterValue = isCreatingNew ? newMasterValue : selectedMaster;
    
    if (!masterValue) return;

    onSave({
      sourceId: item?.id || item?.sourceId,
      sourceValue: item?.sourceValue || item?.source_value,
      masterValue,
      isNewMaster: isCreatingNew
    });

    // Reset form
    setSelectedMaster("");
    setNewMasterValue("");
    setIsCreatingNew(false);
  };

  if (!item) return null;

  const sourceValue = item.sourceValue || item.source_value;
  const frequency = item.frequency || 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader className="bg-gradient-to-r from-blue-100 to-blue-50 -m-6 mb-6 p-6 border-b-2 border-blue-200">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Target className="w-6 h-6 text-blue-600" />
            Match {type}: {sourceValue}
          </DialogTitle>
          <DialogDescription className="text-blue-700">
            Link this source value to a standardized master record for consistent data analysis.
            {frequency > 0 && (
              <span className="block mt-1 text-sm font-medium">
                This item appears {frequency} times in your data
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Business Impact */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <h3 className="font-semibold text-green-800">Why This Matching Matters</h3>
            </div>
            <p className="text-sm text-green-700 mb-2">
              Matching ensures all variations of "{sourceValue}" are treated as the same entity across your analytics.
            </p>
            <div className="text-xs text-green-600 space-y-1">
              <div>✅ Unified reporting and analytics</div>
              <div>📊 Accurate data aggregation</div>
              <div>🎯 Consistent business insights</div>
            </div>
          </div>

          {/* Matching Options */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="create-new">Create New Master Record</Label>
              <Switch
                id="create-new"
                checked={isCreatingNew}
                onCheckedChange={setIsCreatingNew}
              />
            </div>

            {isCreatingNew ? (
              <div className="space-y-3">
                <Label htmlFor="new-master">New Master {type} Name</Label>
                <Input
                  id="new-master"
                  value={newMasterValue}
                  onChange={(e) => setNewMasterValue(e.target.value)}
                  placeholder={`Enter new master ${type.toLowerCase()} name`}
                  className="border-2 border-green-200"
                />
                <div className="text-sm text-gray-600">
                  This will create a new standardized record: <Badge variant="outline" className="ml-1">{newMasterValue || "New Record"}</Badge>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <Label htmlFor="existing-master">Select Existing Master {type}</Label>
                <Select value={selectedMaster} onValueChange={setSelectedMaster}>
                  <SelectTrigger className="border-2 border-blue-200">
                    <SelectValue placeholder={`Choose existing master ${type.toLowerCase()}...`} />
                  </SelectTrigger>
                  <SelectContent>
                    {masterOptions.map((option) => (
                      <SelectItem key={option} value={option}>
                        <div className="flex items-center gap-2">
                          <Building2 className="w-4 h-4" />
                          {option}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedMaster && (
                  <div className="text-sm text-gray-600">
                    This will link to existing record: <Badge variant="outline" className="ml-1">{selectedMaster}</Badge>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Preview */}
          <div className="bg-gray-50 rounded-lg p-4 border">
            <h4 className="font-medium text-gray-800 mb-2 flex items-center gap-2">
              <ArrowRight className="w-4 h-4" />
              Matching Preview
            </h4>
            <div className="text-sm text-gray-600">
              <div>Source: <span className="font-medium">{sourceValue}</span></div>
              <div>Will match to: <span className="font-medium">{isCreatingNew ? newMasterValue || "(new record)" : selectedMaster || "(select option)"}</span></div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSave} 
              className="bg-blue-600 hover:bg-blue-700"
              disabled={!selectedMaster && !newMasterValue}
            >
              <Check className="w-4 h-4 mr-2" />
              Save Matching
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
