
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Target, ArrowRight } from "lucide-react";

interface MappingOptionsProps {
  type: string;
  masterOptions: string[];
  selectedMaster: string;
  setSelectedMaster: (value: string) => void;
  createNew: boolean;
  setCreateNew: (value: boolean) => void;
  newMasterValue: string;
  setNewMasterValue: (value: string) => void;
}

export function MappingOptions({
  type,
  masterOptions,
  selectedMaster,
  setSelectedMaster,
  createNew,
  setCreateNew,
  newMasterValue,
  setNewMasterValue
}: MappingOptionsProps) {
  return (
    <div className="bg-gray-50 rounded-lg p-4 border">
      <h4 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
        <Target className="w-4 h-4" />
        Choose Your Action
      </h4>
      <div className="space-y-4">
        {/* Option 1: Map to Existing */}
        <div className={`border-2 rounded-lg p-4 transition-all ${!createNew ? 'border-orange-300 bg-orange-50' : 'border-gray-200 hover:border-gray-300'}`}>
          <div className="flex items-center space-x-3 mb-3">
            <input
              type="radio"
              id="existing"
              name="mapping-type"
              checked={!createNew}
              onChange={() => setCreateNew(false)}
              className="w-4 h-4 text-orange-600 border-orange-300 focus:ring-orange-500"
            />
            <Label htmlFor="existing" className="text-sm font-medium flex items-center gap-2 cursor-pointer">
              <span>📋</span>
              Map to Existing {type}
              {!createNew && <ArrowRight className="w-4 h-4 text-orange-600" />}
            </Label>
          </div>
          <p className="text-xs text-gray-600 mb-3 ml-7">
            Recommended when you have a similar {type.toLowerCase()} already in your system
          </p>

          {!createNew && (
            <div className="ml-7">
              <Select value={selectedMaster} onValueChange={setSelectedMaster}>
                <SelectTrigger className="border-2 border-orange-200 hover:border-orange-300 focus:border-orange-500">
                  <SelectValue placeholder={`Select existing ${type.toLowerCase()}...`} />
                </SelectTrigger>
                <SelectContent className="border-2 border-orange-200">
                  {masterOptions.map((option) => (
                    <SelectItem key={option} value={option} className="hover:bg-orange-50">
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* Option 2: Create New */}
        <div className={`border-2 rounded-lg p-4 transition-all ${createNew ? 'border-green-300 bg-green-50' : 'border-gray-200 hover:border-gray-300'}`}>
          <div className="flex items-center space-x-3 mb-3">
            <input
              type="radio"
              id="new"
              name="mapping-type"
              checked={createNew}
              onChange={() => setCreateNew(true)}
              className="w-4 h-4 text-green-600 border-green-300 focus:ring-green-500"
            />
            <Label htmlFor="new" className="text-sm font-medium flex items-center gap-2 cursor-pointer">
              <span>✨</span>
              Create New {type}
              {createNew && <ArrowRight className="w-4 h-4 text-green-600" />}
            </Label>
          </div>
          <p className="text-xs text-gray-600 mb-3 ml-7">
            Use when this is truly a new {type.toLowerCase()} not in your system
          </p>

          {createNew && (
            <div className="ml-7">
              <Input
                placeholder={`Enter new ${type.toLowerCase()} name...`}
                value={newMasterValue}
                onChange={(e) => setNewMasterValue(e.target.value)}
                className="border-2 border-green-200 hover:border-green-300 focus:border-green-500"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
