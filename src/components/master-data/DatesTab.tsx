
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Target, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { MatchingModal } from "./MatchingModal";

// Mock data for unmatched date formats
const unmatchedDates = [
  { id: 1, sourceValue: "06/03/24", frequency: 245, source: "Toast POS", lastSeen: "2024-06-03" },
  { id: 2, sourceValue: "2024-06-03 14:30:00", frequency: 189, source: "Square", lastSeen: "2024-06-03" },
  { id: 3, sourceValue: "Jun 3, 2024", frequency: 156, source: "Clover", lastSeen: "2024-06-02" },
  { id: 4, sourceValue: "03/06/2024", frequency: 67, source: "Custom API", lastSeen: "2024-06-03" },
];

// Mock master date formats for matching
const masterDateFormats = [
  "YYYY-MM-DD (ISO Standard)",
  "MM/DD/YYYY (US Format)",
  "DD/MM/YYYY (EU Format)",
  "YYYY-MM-DD HH:MM:SS (DateTime)",
  "Month DD, YYYY (Long Format)",
];

export function DatesTab() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const filteredItems = unmatchedDates.filter(item =>
    item.sourceValue.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleMatchItem = (item) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const handleSaveMatching = (matching) => {
    console.log("Saving date matching:", matching);
    setIsModalOpen(false);
    setSelectedItem(null);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search unmatched date formats..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <Badge variant="secondary" className="bg-orange-100 text-orange-800">
            {filteredItems.length} unmatched formats
          </Badge>
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Source Value</TableHead>
              <TableHead>Source System</TableHead>
              <TableHead>Frequency</TableHead>
              <TableHead>Last Seen</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sourceValue}</TableCell>
                <TableCell>
                  <Badge variant="outline">{item.source}</Badge>
                </TableCell>
                <TableCell>{item.frequency}</TableCell>
                <TableCell>{item.lastSeen}</TableCell>
                <TableCell>
                  <Button
                    size="sm"
                    onClick={() => handleMatchItem(item)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Target className="w-4 h-4 mr-1" />
                    Match
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <MatchingModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        item={selectedItem}
        masterOptions={masterDateFormats}
        onSave={handleSaveMatching}
        type="Date Format"
      />
    </div>
  );
}
