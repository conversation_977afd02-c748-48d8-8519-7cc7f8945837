
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Target, Search, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { MatchingModal } from "./MatchingModal";
import { useUnmatchedItems, useMasterData, useCreateMatching } from "@/hooks/useMasterData";
import { useToast } from "@/hooks/use-toast";

export function MenuCategoriesTab() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { toast } = useToast();

  const { data: unmatchedItems = [], isLoading: isLoadingUnmatched } = useUnmatchedItems('menu-categories');
  const { data: masterItems = [], isLoading: isLoadingMaster } = useMasterData('menu-categories');
  const createMatching = useCreateMatching();

  const filteredItems = unmatchedItems.filter(item =>
    item.source_value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleMatchItem = (item) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const handleSaveMatching = async (matching) => {
    try {
      await createMatching.mutateAsync({
        sourceId: matching.sourceId,
        sourceValue: matching.sourceValue,
        masterValue: matching.masterValue,
        isNewMaster: matching.isNewMaster,
        dataType: 'menu-categories',
        masterTable: 'master_menu_categories',
        masterId: matching.isNewMaster ? undefined : masterItems.find(item => item.name === matching.masterValue)?.id
      });

      toast({
        title: "Matching saved successfully",
        description: `"${matching.sourceValue}" has been matched to "${matching.masterValue}"`,
      });

      setIsModalOpen(false);
      setSelectedItem(null);
    } catch (error) {
      console.error("Error saving matching:", error);
      toast({
        title: "Error saving matching",
        description: "Please try again later",
        variant: "destructive",
      });
    }
  };

  if (isLoadingUnmatched || isLoadingMaster) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin" />
        <span className="ml-2">Loading data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search unmatched menu categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <Badge variant="secondary" className="bg-orange-100 text-orange-800">
            {filteredItems.length} unmatched categories
          </Badge>
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Source Value</TableHead>
              <TableHead>Source System</TableHead>
              <TableHead>Frequency</TableHead>
              <TableHead>Last Seen</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.source_value}</TableCell>
                <TableCell>
                  <Badge variant="outline">{item.source_system.name}</Badge>
                </TableCell>
                <TableCell>{item.frequency}</TableCell>
                <TableCell>{item.last_seen}</TableCell>
                <TableCell>
                  <Button
                    size="sm"
                    onClick={() => handleMatchItem(item)}
                    className="bg-blue-600 hover:bg-blue-700"
                    disabled={createMatching.isPending}
                  >
                    <Target className="w-4 h-4 mr-1" />
                    Match
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <MatchingModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        item={selectedItem}
        masterOptions={masterItems.map(item => item.name)}
        onSave={handleSaveMatching}
        type="Menu Category"
      />
    </div>
  );
}
