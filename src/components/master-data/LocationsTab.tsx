
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Target, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { MatchingModal } from "./MatchingModal";

// Mock data for unmatched locations
const unmatchedLocations = [{
  id: 1,
  sourceValue: "NYC Times Square",
  frequency: 89,
  source: "Toast POS",
  lastSeen: "2024-06-03"
}, {
  id: 2,
  sourceValue: "Los Angeles Downtown",
  frequency: 67,
  source: "Square",
  lastSeen: "2024-06-03"
}, {
  id: 3,
  sourceValue: "Chicago Loop",
  frequency: 134,
  source: "Clover",
  lastSeen: "2024-06-02"
}, {
  id: 4,
  sourceValue: "Miami Beach Store",
  frequency: 45,
  source: "Toast POS",
  lastSeen: "2024-06-03"
}];

// Mock master locations for matching
const masterLocations = ["New York - Times Square", "Los Angeles - Downtown", "Chicago - The Loop", "Miami - South Beach", "San Francisco - Union Square", "Boston - Back Bay"];

export function LocationsTab() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedItem, setSelectedItem] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const filteredItems = unmatchedLocations.filter(item => item.sourceValue.toLowerCase().includes(searchTerm.toLowerCase()));
  const handleMatchItem = item => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };
  const handleSaveMatching = matching => {
    console.log("Saving location matching:", matching);
    setIsModalOpen(false);
    setSelectedItem(null);
  };
  return <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="Search unmatched locations..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="pl-10 w-80" />
          </div>
          <Badge variant="secondary" className="bg-orange-100 text-orange-800">
            {filteredItems.length} unmatched locations
          </Badge>
        </div>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Source Value</TableHead>
              <TableHead>Source System</TableHead>
              <TableHead>Frequency</TableHead>
              <TableHead>Last Seen</TableHead>
              <TableHead>Master Data Record</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.map(item => <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sourceValue}</TableCell>
                <TableCell>
                  <Badge variant="outline">{item.source}</Badge>
                </TableCell>
                <TableCell>{item.frequency}</TableCell>
                <TableCell>{item.lastSeen}</TableCell>
                <TableCell>
                  <Button size="sm" onClick={() => handleMatchItem(item)} className="bg-blue-600 hover:bg-blue-700">
                    <Target className="w-4 h-4 mr-1" />
                    Match
                  </Button>
                </TableCell>
              </TableRow>)}
          </TableBody>
        </Table>
      </div>

      <MatchingModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} item={selectedItem} masterOptions={masterLocations} onSave={handleSaveMatching} type="Location" />
    </div>;
}
