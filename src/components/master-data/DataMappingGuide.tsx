
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  HelpCircle, 
  ChevronDown, 
  ChevronUp
} from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { getDataTypeInfo } from "./guide/DataTypeInfo";
import { ExampleMappingSection } from "./guide/ExampleMappingSection";
import { BusinessBenefitsSection } from "./guide/BusinessBenefitsSection";
import { BestPracticesSection } from "./guide/BestPracticesSection";

interface DataMappingGuideProps {
  dataType: string;
  className?: string;
}

export function DataMappingGuide({ dataType, className }: DataMappingGuideProps) {
  const [isOpen, setIsOpen] = useState(false);
  const typeInfo = getDataTypeInfo(dataType);

  return (
    <Card className={`border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 ${className}`}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-blue-100 transition-colors">
            <CardTitle className="flex items-center justify-between text-blue-800">
              <div className="flex items-center gap-2">
                <HelpCircle className="w-5 h-5" />
                <span className="text-lg">{typeInfo.icon} {typeInfo.title} Mapping Guide</span>
              </div>
              {isOpen ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
            </CardTitle>
          </CardHeader>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="space-y-6">
            <p className="text-blue-700">{typeInfo.description}</p>

            <ExampleMappingSection typeInfo={typeInfo} />
            <BusinessBenefitsSection typeInfo={typeInfo} />
            <BestPracticesSection typeInfo={typeInfo} />

            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setIsOpen(false)}
              className="w-full border-blue-300 text-blue-700 hover:bg-blue-50"
            >
              Got it! Close Guide
            </Button>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
