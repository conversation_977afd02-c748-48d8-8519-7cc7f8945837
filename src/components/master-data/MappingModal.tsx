
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Check, X, Database, Sparkles } from "lucide-react";
import { BusinessValueCard } from "./BusinessValueCard";
import { SourceDataCard } from "./SourceDataCard";
import { MappingOptions } from "./MappingOptions";
import { MappingExample } from "./MappingExample";
import { MappingOutcome } from "./MappingOutcome";

interface MappingModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: any;
  masterOptions: string[];
  onSave: (mapping: any) => void;
  type: string;
}

const getBusinessValueTitle = (type: string) => {
  const titles = {
    "Menu Item": "Standardize Your Menu Items",
    "Menu Category": "Organize Your Menu Structure", 
    "Survey Question": "Standardize Customer Feedback",
    "Employee": "Connect Employee Data",
    "Ingredient": "Standardize Inventory Items"
  };
  
  return titles[type] || titles["Menu Item"];
};

export function MappingModal({ isOpen, onClose, item, masterOptions, onSave, type }: MappingModalProps) {
  const [selectedMaster, setSelectedMaster] = useState("");
  const [createNew, setCreateNew] = useState(false);
  const [newMasterValue, setNewMasterValue] = useState("");

  useEffect(() => {
    if (isOpen) {
      setSelectedMaster("");
      setCreateNew(false);
      setNewMasterValue("");
    }
  }, [isOpen]);

  const handleSave = () => {
    const mapping = {
      sourceId: item?.id,
      sourceValue: item?.sourceValue,
      masterValue: createNew ? newMasterValue : selectedMaster,
      isNewMaster: createNew,
    };
    onSave(mapping);
  };

  const isValid = createNew ? newMasterValue.trim() !== "" : selectedMaster !== "";

  if (!item) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl border-2 border-orange-200 max-h-[90vh] overflow-y-auto">
        <DialogHeader className="bg-gradient-to-r from-orange-100 to-orange-50 -m-6 mb-6 p-6 border-b-2 border-orange-200">
          <DialogTitle className="flex items-center gap-2 text-xl text-gray-800">
            <Database className="w-6 h-6 text-orange-600" />
            {getBusinessValueTitle(type)}
            <Sparkles className="w-4 h-4 text-orange-500" />
          </DialogTitle>
        </DialogHeader>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Left Column - Business Value */}
          <div className="lg:col-span-1 space-y-4">
            <BusinessValueCard type={type} />
            <MappingExample type={type} />
          </div>

          {/* Right Column - Mapping Interface */}
          <div className="lg:col-span-2 space-y-6">
            <SourceDataCard item={item} />

            <MappingOptions
              type={type}
              masterOptions={masterOptions}
              selectedMaster={selectedMaster}
              setSelectedMaster={setSelectedMaster}
              createNew={createNew}
              setCreateNew={setCreateNew}
              newMasterValue={newMasterValue}
              setNewMasterValue={setNewMasterValue}
            />

            <MappingOutcome type={type} />
          </div>
        </div>

        <DialogFooter className="gap-2 mt-6">
          <Button variant="outline" onClick={onClose} className="border-2 border-gray-200 hover:bg-gray-50">
            <X className="w-4 h-4 mr-1" />
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!isValid}
            className={`border-0 text-white ${
              createNew 
                ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' 
                : 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'
            }`}
          >
            <Check className="w-4 h-4 mr-1" />
            {createNew ? 'Create & Map' : 'Map to Existing'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
