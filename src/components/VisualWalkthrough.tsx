
import React, { useState, useEffect, useCallback } from 'react';
import { calculateCardPosition } from '@/utils/walkthroughUtils';
import { SpotlightOverlay } from '@/components/walkthrough/SpotlightOverlay';
import { HighlightRing } from '@/components/walkthrough/HighlightRing';
import { WalkthroughCard } from '@/components/walkthrough/WalkthroughCard';
import { WalkthroughStep } from '@/types/walkthrough';

interface VisualWalkthroughProps {
  steps: WalkthroughStep[];
  onComplete: () => void;
  onSkip: () => void;
}

export const VisualWalkthrough: React.FC<VisualWalkthroughProps> = ({ 
  steps, 
  onComplete, 
  onSkip 
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [highlightedElement, setHighlightedElement] = useState<Element | null>(null);
  const [cardPosition, setCardPosition] = useState({ top: 0, left: 0 });
  const [arrowDirection, setArrowDirection] = useState<'up' | 'down' | 'left' | 'right'>('down');

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const step = steps[currentStep];
    if (step && isVisible) {
      const element = document.querySelector(step.target);
      setHighlightedElement(element);
      
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Calculate card position after a brief delay to ensure scrolling is complete
        setTimeout(() => {
          const { top, left, arrow } = calculateCardPosition(element, step.position);
          setCardPosition({ top, left });
          setArrowDirection(arrow);
        }, 500);
      }
    }
  }, [currentStep, isVisible, steps]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    setIsVisible(false);
    onComplete();
  };

  const handleSkip = () => {
    setIsVisible(false);
    onSkip();
  };

  if (!isVisible) return null;

  const step = steps[currentStep];
  
  return (
    <>
      <SpotlightOverlay highlightedElement={highlightedElement} />
      <HighlightRing highlightedElement={highlightedElement} />
      <WalkthroughCard
        step={step}
        currentStep={currentStep}
        totalSteps={steps.length}
        cardPosition={cardPosition}
        arrowDirection={arrowDirection}
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSkip={handleSkip}
      />
    </>
  );
};
