
import React from 'react';
import { VisualWalkthrough } from '@/components/VisualWalkthrough';
import { WalkthroughTrigger } from '@/components/WalkthroughTrigger';
import { usePageWalkthrough } from '@/hooks/usePageWalkthrough';
import { WalkthroughStep } from '@/types/walkthrough';

interface PageWalkthroughProps {
  steps: WalkthroughStep[];
  pageKey: string;
  autoStart?: boolean;
  triggerClassName?: string;
}

export const PageWalkthrough: React.FC<PageWalkthroughProps> = ({
  steps,
  pageKey,
  autoStart = true,
  triggerClassName
}) => {
  const {
    showWalkthrough,
    hasSeenWalkthrough,
    completeWalkthrough,
    skipWalkthrough,
    startWalkthrough
  } = usePageWalkthrough({ steps, pageKey, autoStart });

  return (
    <>
      {showWalkthrough && (
        <VisualWalkthrough
          steps={steps}
          onComplete={completeWalkthrough}
          onSkip={skipWalkthrough}
        />
      )}
      
      {hasSeenWalkthrough && (
        <WalkthroughTrigger 
          onStart={startWalkthrough}
          className={triggerClassName}
        />
      )}
    </>
  );
};
