
import React from 'react';

interface SpotlightOverlayProps {
  highlightedElement: Element | null;
}

export const SpotlightOverlay: React.FC<SpotlightOverlayProps> = ({ highlightedElement }) => {
  return (
    <div className="fixed inset-0 z-40" style={{ pointerEvents: 'none' }}>
      <div 
        className="absolute inset-0 bg-black bg-opacity-75"
        style={{
          clipPath: highlightedElement 
            ? `polygon(0% 0%, 0% 100%, ${highlightedElement.getBoundingClientRect().left - 8}px 100%, ${highlightedElement.getBoundingClientRect().left - 8}px ${highlightedElement.getBoundingClientRect().top - 8}px, ${highlightedElement.getBoundingClientRect().right + 8}px ${highlightedElement.getBoundingClientRect().top - 8}px, ${highlightedElement.getBoundingClientRect().right + 8}px ${highlightedElement.getBoundingClientRect().bottom + 8}px, ${highlightedElement.getBoundingClientRect().left - 8}px ${highlightedElement.getBoundingClientRect().bottom + 8}px, ${highlightedElement.getBoundingClientRect().left - 8}px 100%, 100% 100%, 100% 0%)`
            : 'none'
        }}
      />
    </div>
  );
};
