
import React from 'react';

interface HighlightRingProps {
  highlightedElement: Element | null;
}

export const HighlightRing: React.FC<HighlightRingProps> = ({ highlightedElement }) => {
  if (!highlightedElement) return null;

  return (
    <div 
      className="fixed z-50 pointer-events-none animate-pulse walkthrough-highlight-ring"
      style={{
        top: highlightedElement.getBoundingClientRect().top - 12,
        left: highlightedElement.getBoundingClientRect().left - 12,
        width: highlightedElement.getBoundingClientRect().width + 24,
        height: highlightedElement.getBoundingClientRect().height + 24,
        border: '4px solid #ea580c',
        borderRadius: '16px',
        boxShadow: '0 0 0 8px rgba(234, 88, 12, 0.3), 0 0 30px rgba(234, 88, 12, 0.6)',
      }}
    />
  );
};
