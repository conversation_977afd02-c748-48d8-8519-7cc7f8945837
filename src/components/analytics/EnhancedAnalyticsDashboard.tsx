
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ExecutiveDashboard } from './ExecutiveDashboard';
import { useUserProfile } from '@/hooks/useUserProfile';
import { Calendar, Download, Filter, RefreshCw } from 'lucide-react';

interface EnhancedAnalyticsDashboardProps {
  locationId?: string;
  dateRange?: { start: string; end: string };
}

export function EnhancedAnalyticsDashboard({ locationId, dateRange }: EnhancedAnalyticsDashboardProps) {
  const { profile } = useUserProfile();
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  
  // Determine user role for dashboard customization
  const getUserRole = () => {
    if (!profile?.role) return 'CEO';
    
    if (profile.role.toLowerCase().includes('ceo') || profile.role.toLowerCase().includes('chief executive')) {
      return 'CEO';
    } else if (profile.role.toLowerCase().includes('coo') || profile.role.toLowerCase().includes('operations')) {
      return 'COO';
    } else if (profile.role.toLowerCase().includes('cfo') || profile.role.toLowerCase().includes('financial')) {
      return 'CFO';
    }
    
    return 'CEO'; // Default to CEO view
  };

  const userRole = getUserRole() as 'CEO' | 'COO' | 'CFO';

  const getRoleTitle = () => {
    switch (userRole) {
      case 'CEO':
        return 'Chief Executive Dashboard';
      case 'COO':
        return 'Chief Operating Officer Dashboard';
      case 'CFO':
        return 'Chief Financial Officer Dashboard';
      default:
        return 'Executive Dashboard';
    }
  };

  const getRoleDescription = () => {
    switch (userRole) {
      case 'CEO':
        return 'Strategic overview, growth metrics, and market performance indicators';
      case 'COO':
        return 'Operational efficiency, customer experience, and location performance metrics';
      case 'CFO':
        return 'Financial performance, cost analysis, and profitability insights';
      default:
        return 'Comprehensive business analytics and insights';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            {getRoleTitle()}
            <Badge variant="secondary" className="ml-3">
              {userRole}
            </Badge>
          </h1>
          <p className="text-gray-600 mt-2">{getRoleDescription()}</p>
        </div>
        
        <div className="flex space-x-3">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedLocation} onValueChange={setSelectedLocation}>
            <SelectTrigger className="w-40">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="All Locations" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Locations</SelectItem>
              <SelectItem value="downtown">Downtown</SelectItem>
              <SelectItem value="mall">Mall Location</SelectItem>
              <SelectItem value="airport">Airport Location</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Role-Specific Executive Dashboard */}
      <ExecutiveDashboard userRole={userRole} tenantKey={1} />

      {/* AI Insights Panel */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-900">
            🤖 AI Business Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="p-3 bg-white/60 rounded-lg">
              <h4 className="font-semibold text-blue-900">Revenue Optimization Opportunity</h4>
              <p className="text-blue-800 text-sm mt-1">
                Analysis suggests implementing dynamic pricing during peak hours could increase revenue by 8-12%. 
                Airport location shows highest potential with 89% customer price acceptance rate.
              </p>
            </div>
            
            <div className="p-3 bg-white/60 rounded-lg">
              <h4 className="font-semibold text-blue-900">Operational Efficiency Alert</h4>
              <p className="text-blue-800 text-sm mt-1">
                Labor scheduling optimization could reduce costs by $34,000 annually while maintaining service quality. 
                Recommend implementing AI-powered scheduling for evening shifts.
              </p>
            </div>
            
            <div className="p-3 bg-white/60 rounded-lg">
              <h4 className="font-semibold text-blue-900">Customer Experience Enhancement</h4>
              <p className="text-blue-800 text-sm mt-1">
                Customer sentiment analysis indicates 23% satisfaction increase potential through personalized menu recommendations. 
                Loyalty program expansion could drive 15% repeat visit growth.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
