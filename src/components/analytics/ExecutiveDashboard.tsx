
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useExecutiveMetrics, useLocationPerformance, useTrendAnalysis, useAnomalyDetection } from '@/hooks/useEnhancedAnalytics';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, Users, AlertTriangle, Target, Award, MapPin } from 'lucide-react';

interface ExecutiveDashboardProps {
  userRole: 'CEO' | 'COO' | 'CFO';
  tenantKey?: number;
}

export function ExecutiveDashboard({ userRole, tenantKey = 1 }: ExecutiveDashboardProps) {
  const { data: metrics, isLoading: metricsLoading } = useExecutiveMetrics(tenantKey);
  const { data: locations, isLoading: locationsLoading } = useLocationPerformance(tenantKey);
  const { data: trends, isLoading: trendsLoading } = useTrendAnalysis(tenantKey);
  const { data: anomalies, isLoading: anomaliesLoading } = useAnomalyDetection(tenantKey);

  if (metricsLoading || locationsLoading || trendsLoading || anomaliesLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-24 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!metrics || !locations || !trends || !anomalies) return null;

  const getMetricsForRole = () => {
    switch (userRole) {
      case 'CEO':
        return [
          {
            title: 'Total Revenue',
            value: `$${metrics.financial.totalRevenue.toLocaleString()}`,
            change: `+${metrics.financial.revenueGrowth}%`,
            icon: DollarSign,
            trend: 'up'
          },
          {
            title: 'Same-Store Sales Growth',
            value: `${metrics.strategic.sameStoreSalesGrowth}%`,
            change: '+2.1% vs target',
            icon: TrendingUp,
            trend: 'up'
          },
          {
            title: 'Customer Lifetime Value',
            value: `$${metrics.strategic.customerLifetimeValue}`,
            change: '+$23 vs last quarter',
            icon: Users,
            trend: 'up'
          },
          {
            title: 'Market Share',
            value: `${metrics.strategic.marketShare}%`,
            change: '+0.8% vs last year',
            icon: Target,
            trend: 'up'
          }
        ];
      case 'COO':
        return [
          {
            title: 'Customer Satisfaction',
            value: `${metrics.operational.customerSatisfactionScore}/5.0`,
            change: '+0.2 vs last month',
            icon: Award,
            trend: 'up'
          },
          {
            title: 'Average Service Time',
            value: `${metrics.operational.averageServiceTime} min`,
            change: '-1.2 min improvement',
            icon: TrendingDown,
            trend: 'up'
          },
          {
            title: 'Table Utilization',
            value: `${metrics.operational.tableUtilization}%`,
            change: '+3.4% vs last month',
            icon: MapPin,
            trend: 'up'
          },
          {
            title: 'Labor Efficiency',
            value: `${(100 - metrics.riskIndicators.laborVariance).toFixed(1)}%`,
            change: 'Within target range',
            icon: Users,
            trend: 'stable'
          }
        ];
      case 'CFO':
        return [
          {
            title: 'Gross Margin',
            value: `${metrics.financial.grossMargin}%`,
            change: '+1.2% vs target',
            icon: DollarSign,
            trend: 'up'
          },
          {
            title: 'Net Margin',
            value: `${metrics.financial.netMargin}%`,
            change: '+0.8% vs last quarter',
            icon: TrendingUp,
            trend: 'up'
          },
          {
            title: 'Cost of Goods Sold',
            value: `$${metrics.financial.costOfGoodsSold.toLocaleString()}`,
            change: 'On target',
            icon: TrendingDown,
            trend: 'stable'
          },
          {
            title: 'Cash Flow Risk',
            value: `${metrics.riskIndicators.cashFlowRisk}%`,
            change: 'Low risk level',
            icon: AlertTriangle,
            trend: 'stable'
          }
        ];
      default:
        return [];
    }
  };

  const roleMetrics = getMetricsForRole();
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="space-y-6">
      {/* Executive Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {roleMetrics.map((metric, index) => (
          <Card key={index} className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-2xl font-bold">{metric.value}</p>
                  <p className={`text-sm ${metric.trend === 'up' ? 'text-green-600' : metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'}`}>
                    {metric.change}
                  </p>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg">
                  <metric.icon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Anomaly Alerts */}
      {anomalies.anomalies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
              Critical Business Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {anomalies.anomalies.slice(0, 3).map((anomaly) => (
                <Alert key={anomaly.id} className={`border-l-4 ${
                  anomaly.severity === 'high' ? 'border-l-red-500' : 
                  anomaly.severity === 'medium' ? 'border-l-yellow-500' : 'border-l-blue-500'
                }`}>
                  <AlertDescription>
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">{anomaly.description}</p>
                        <p className="text-sm text-gray-600 mt-1">
                          Location: {anomaly.location} • Impact: {anomaly.impact}
                        </p>
                        <p className="text-sm text-blue-600 mt-1">
                          Recommended: {anomaly.recommendedAction}
                        </p>
                      </div>
                      <Badge variant={
                        anomaly.severity === 'high' ? 'destructive' : 
                        anomaly.severity === 'medium' ? 'secondary' : 'default'
                      }>
                        {anomaly.severity}
                      </Badge>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="performance" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="performance">Performance Overview</TabsTrigger>
          <TabsTrigger value="locations">Location Analysis</TabsTrigger>
          <TabsTrigger value="trends">Trend Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue vs Profit Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue & Profit Trend (30 Days)</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip formatter={(value, name) => [
                      `$${Number(value).toLocaleString()}`, 
                      name === 'revenue' ? 'Revenue' : 'Profit'
                    ]} />
                    <Line type="monotone" dataKey="revenue" stroke="#3b82f6" strokeWidth={2} />
                    <Line type="monotone" dataKey="profit" stroke="#10b981" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Cost Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Structure</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Food Cost', value: metrics.financial.costOfGoodsSold },
                        { name: 'Labor Cost', value: metrics.financial.operatingExpenses * 0.6 },
                        { name: 'Rent & Utilities', value: metrics.financial.operatingExpenses * 0.25 },
                        { name: 'Other Expenses', value: metrics.financial.operatingExpenses * 0.15 }
                      ]}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                    >
                      {COLORS.map((color, index) => (
                        <Cell key={`cell-${index}`} fill={color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Amount']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="locations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Location Performance Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {locations.map((location) => (
                  <div key={location.locationKey} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-semibold text-lg">{location.locationName}</h3>
                        <p className="text-sm text-gray-600">Performance Score: {location.performanceScore}/100</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold">${location.revenue.toLocaleString()}</p>
                        <p className={`text-sm ${location.revenueGrowth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {location.revenueGrowth > 0 ? '+' : ''}{location.revenueGrowth}% growth
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 mt-3">
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Profit Margin</p>
                        <p className="font-semibold">{location.profitMargin}%</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Customers</p>
                        <p className="font-semibold">{location.customerCount.toLocaleString()}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Avg Order</p>
                        <p className="font-semibold">${location.averageOrderValue}</p>
                      </div>
                    </div>
                    {location.alerts.length > 0 && (
                      <div className="mt-3 pt-3 border-t">
                        <p className="text-sm font-medium text-orange-600">Active Alerts:</p>
                        <ul className="text-sm text-gray-600 mt-1">
                          {location.alerts.map((alert, index) => (
                            <li key={index}>• {alert}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Customer Count Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Traffic Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip formatter={(value) => [Number(value).toLocaleString(), 'Customers']} />
                    <Line type="monotone" dataKey="customerCount" stroke="#8b5cf6" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Cost Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip formatter={(value, name) => [
                      `$${Number(value).toLocaleString()}`, 
                      name === 'laborCost' ? 'Labor Cost' : 'Food Cost'
                    ]} />
                    <Line type="monotone" dataKey="laborCost" stroke="#ef4444" strokeWidth={2} />
                    <Line type="monotone" dataKey="foodCost" stroke="#f59e0b" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
