
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp } from 'lucide-react';

interface Prediction {
  prediction_key: number;
  prediction_type: string;
  target_metric: string;
  forecast_date: string;
  predicted_value: number;
  confidence_level: number;
  model_name: string;
}

interface PredictiveAnalyticsListProps {
  predictions?: Prediction[];
}

export function PredictiveAnalyticsList({ predictions }: PredictiveAnalyticsListProps) {
  if (!predictions || predictions.length === 0) {
    return (
      <Card className="md:col-span-2">
        <CardContent className="p-8 text-center">
          <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Predictions</h3>
          <p className="text-gray-600">Predictive models are being prepared and will show forecasts soon.</p>
        </Card<PERSON>ontent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {predictions.map((prediction) => (
        <Card key={prediction.prediction_key}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg capitalize">
                {prediction.prediction_type.replace('_', ' ')} Forecast
              </CardTitle>
              <Badge variant="outline">
                {(prediction.confidence_level * 100).toFixed(0)}% Confidence
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><span className="font-medium">Target:</span> {prediction.target_metric}</p>
              <p><span className="font-medium">Forecast Date:</span> {new Date(prediction.forecast_date).toLocaleDateString()}</p>
              <p><span className="font-medium">Predicted Value:</span> {prediction.predicted_value.toLocaleString()}</p>
              <p><span className="font-medium">Model:</span> {prediction.model_name}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
