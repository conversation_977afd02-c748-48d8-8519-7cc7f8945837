
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Brain, AlertTriangle, TrendingUp, Target } from 'lucide-react';

interface AIInsightsMetricsProps {
  insightsCount: number;
  anomaliesCount: number;
  predictionsCount: number;
  criticalCount: number;
}

export function AIInsightsMetrics({
  insightsCount,
  anomaliesCount,
  predictionsCount,
  criticalCount
}: AIInsightsMetricsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Insights</p>
              <p className="text-2xl font-bold">{insightsCount}</p>
            </div>
            <Brain className="h-8 w-8 text-blue-600" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Anomalies Detected</p>
              <p className="text-2xl font-bold">{anomaliesCount}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-orange-600" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Predictions</p>
              <p className="text-2xl font-bold">{predictionsCount}</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-600" />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical Issues</p>
              <p className="text-2xl font-bold">{criticalCount}</p>
            </div>
            <Target className="h-8 w-8 text-red-600" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
