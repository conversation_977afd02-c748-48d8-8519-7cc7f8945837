
import React, { useState } from 'react';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useAIInsights, useAnomalyDetections, usePredictiveAnalytics, useResolveInsight, useConfirmAnomaly } from '@/hooks/useAIAnalytics';
import { AIModelPerformance } from './AIModelPerformance';
import { AIInsightsHeader } from './AIInsightsHeader';
import { AIInsightsMetrics } from './AIInsightsMetrics';
import { AIInsightsList } from './AIInsightsList';
import { AnomalyDetectionList } from './AnomalyDetectionList';
import { PredictiveAnalyticsList } from './PredictiveAnalyticsList';
import { RoleProtectedComponent } from '@/components/RoleProtectedComponent';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Lightbulb, AlertTriangle, TrendingUp, Activity, Brain } from 'lucide-react';

export function AIInsightsDashboard({ tenantKey = 1 }: { tenantKey?: number }) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  
  const { data: insights, isLoading: insightsLoading, error: insightsError } = useAIInsights(tenantKey, {
    category: selectedCategory !== 'all' ? selectedCategory : undefined,
    severity: selectedSeverity !== 'all' ? selectedSeverity : undefined
  });
  
  const { data: anomalies, isLoading: anomaliesLoading, error: anomaliesError } = useAnomalyDetections(tenantKey);
  const { data: predictions, isLoading: predictionsLoading, error: predictionsError } = usePredictiveAnalytics(tenantKey);
  
  const resolveInsightMutation = useResolveInsight();
  const confirmAnomalyMutation = useConfirmAnomaly();

  const handleResolveInsight = (insightKey: number) => {
    resolveInsightMutation.mutate({ insightKey });
  };

  const handleConfirmAnomaly = (anomalyKey: number) => {
    confirmAnomalyMutation.mutate({ anomalyKey });
  };

  // Check for permission-related errors
  const hasPermissionError = insightsError?.message?.includes('row-level security') ||
                             anomaliesError?.message?.includes('row-level security') ||
                             predictionsError?.message?.includes('row-level security');

  if (hasPermissionError) {
    return (
      <div className="space-y-6">
        <Alert className="border-orange-200 bg-orange-50">
          <Brain className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            <div className="space-y-2">
              <p className="font-medium">AI Analytics Access Required</p>
              <p className="text-sm">
                You need appropriate permissions to access AI analytics data. Please contact your administrator
                or ensure you have the correct role assigned to your account.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (insightsLoading || anomaliesLoading || predictionsLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse bg-gray-200 rounded h-32"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AIInsightsHeader
        selectedCategory={selectedCategory}
        selectedSeverity={selectedSeverity}
        onCategoryChange={setSelectedCategory}
        onSeverityChange={setSelectedSeverity}
      />

      <AIInsightsMetrics
        insightsCount={insights?.length || 0}
        anomaliesCount={anomalies?.length || 0}
        predictionsCount={predictions?.length || 0}
        criticalCount={insights?.filter(i => i.severity === 'critical').length || 0}
      />

      <Tabs defaultValue="insights" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 h-16 p-2 bg-orange-50 border-2 border-orange-200 rounded-xl">
          <TabsTrigger 
            value="insights" 
            className="h-12 text-sm font-semibold rounded-lg transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-orange-700 data-[state=active]:shadow-md data-[state=active]:border-orange-300 hover:bg-orange-100/50 flex items-center gap-2"
          >
            <Lightbulb className="h-4 w-4" />
            AI Insights
          </TabsTrigger>
          <TabsTrigger 
            value="anomalies" 
            className="h-12 text-sm font-semibold rounded-lg transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-orange-700 data-[state=active]:shadow-md data-[state=active]:border-orange-300 hover:bg-orange-100/50 flex items-center gap-2"
          >
            <AlertTriangle className="h-4 w-4" />
            Anomaly Detection
          </TabsTrigger>
          <TabsTrigger 
            value="predictions" 
            className="h-12 text-sm font-semibold rounded-lg transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-orange-700 data-[state=active]:shadow-md data-[state=active]:border-orange-300 hover:bg-orange-100/50 flex items-center gap-2"
          >
            <TrendingUp className="h-4 w-4" />
            Predictive Analytics
          </TabsTrigger>
          <TabsTrigger 
            value="models" 
            className="h-12 text-sm font-semibold rounded-lg transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-orange-700 data-[state=active]:shadow-md data-[state=active]:border-orange-300 hover:bg-orange-100/50 flex items-center gap-2"
          >
            <Activity className="h-4 w-4" />
            Model Performance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="space-y-4">
          <AIInsightsList
            insights={insights}
            onResolveInsight={handleResolveInsight}
            isResolving={resolveInsightMutation.isPending}
          />
        </TabsContent>

        <TabsContent value="anomalies" className="space-y-4">
          <AnomalyDetectionList
            anomalies={anomalies}
            onConfirmAnomaly={handleConfirmAnomaly}
            isConfirming={confirmAnomalyMutation.isPending}
          />
        </TabsContent>

        <TabsContent value="predictions" className="space-y-4">
          <PredictiveAnalyticsList predictions={predictions} />
        </TabsContent>

        <TabsContent value="models">
          <RoleProtectedComponent allowedRoles={['CEO', 'COO', 'Data Admin']}>
            <AIModelPerformance />
          </RoleProtectedComponent>
        </TabsContent>
      </Tabs>
    </div>
  );
}
