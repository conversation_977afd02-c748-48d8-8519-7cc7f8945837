
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, CheckCircle } from 'lucide-react';

interface Anomaly {
  anomaly_key: number;
  metric_name: string;
  expected_value: number;
  actual_value: number;
  deviation_percentage: number;
  business_impact: string;
  anomaly_score: number;
  is_confirmed: boolean;
}

interface AnomalyDetectionListProps {
  anomalies?: Anomaly[];
  onConfirmAnomaly: (anomalyKey: number) => void;
  isConfirming: boolean;
}

export function AnomalyDetectionList({ anomalies, onConfirmAnomaly, isConfirming }: AnomalyDetectionListProps) {
  if (!anomalies || anomalies.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Anomalies Detected</h3>
          <p className="text-gray-600">All metrics are within expected ranges.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {anomalies.map((anomaly) => (
        <Alert key={anomaly.anomaly_key} className="border-l-4 border-l-orange-500">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="flex justify-between items-start">
              <div>
                <p className="font-medium">{anomaly.metric_name} Anomaly Detected</p>
                <p className="text-sm text-gray-600 mt-1">
                  Expected: {anomaly.expected_value} | Actual: {anomaly.actual_value} 
                  ({anomaly.deviation_percentage > 0 ? '+' : ''}{anomaly.deviation_percentage}%)
                </p>
                <p className="text-sm text-gray-600 mt-1">{anomaly.business_impact}</p>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">
                  Score: {(anomaly.anomaly_score * 100).toFixed(0)}%
                </Badge>
                {!anomaly.is_confirmed && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onConfirmAnomaly(anomaly.anomaly_key)}
                    disabled={isConfirming}
                  >
                    Confirm
                  </Button>
                )}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      ))}
    </div>
  );
}
