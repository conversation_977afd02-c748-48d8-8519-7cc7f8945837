
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { TrendingUp, Database, CheckCircle, AlertTriangle, DollarSign } from "lucide-react";
import { useDashboardMetrics } from "@/hooks/useRestaurantData";
import { Skeleton } from "@/components/ui/skeleton";

export const MetricsCards = () => {
  const { data: metrics, isLoading, error } = useDashboardMetrics();

  if (error) {
    console.error('Metrics error:', error);
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Today's Data Volume</CardTitle>
          <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
            <DollarSign className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-24" />
          ) : (
            <>
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(metrics?.totalRevenue || 0)}
              </div>
              <p className="text-xs text-orange-600 font-medium">
                📊 {metrics?.transactionCount || 0} transactions processed
              </p>
            </>
          )}
        </CardContent>
      </Card>

      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Active Integrations</CardTitle>
          <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
            <Database className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <>
              <div className="text-2xl font-bold text-gray-900">{metrics?.activeIntegrations || 0}</div>
              <p className="text-xs text-orange-600 font-medium">
                🔗 Data sources connected
              </p>
            </>
          )}
        </CardContent>
      </Card>

      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Data Quality Score</CardTitle>
          <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
            <TrendingUp className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <>
              <div className="text-2xl font-bold text-gray-900">{metrics?.dataQuality || 0}%</div>
              <p className="text-xs text-orange-600 font-medium">
                ✨ Data standardization rate
              </p>
            </>
          )}
        </CardContent>
      </Card>

      <Card className="border-2 border-orange-200 bg-gradient-to-br from-white to-orange-50 hover:shadow-lg hover:shadow-orange-100/50 transition-all">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Integration Health</CardTitle>
          <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
            <CheckCircle className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-8 w-20" />
          ) : (
            <>
              <div className="text-2xl font-bold text-green-600">Healthy</div>
              <p className="text-xs text-orange-600 font-medium">
                ⚡ All systems operational
              </p>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
