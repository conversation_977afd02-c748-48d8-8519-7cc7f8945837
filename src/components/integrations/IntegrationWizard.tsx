
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { X } from "lucide-react";
import { IntegrationTypeSelector } from "./IntegrationTypeSelector";
import { IntegrationConfiguration } from "./IntegrationConfiguration";
import { ConnectionTest } from "./ConnectionTest";
import { DataMapping } from "./DataMapping";
import { IntegrationConfirmation } from "./IntegrationConfirmation";

interface IntegrationWizardProps {
  onClose: () => void;
}

export interface IntegrationData {
  type?: string;
  name?: string;
  config?: Record<string, any>;
  testResults?: any;
  dataMapping?: any;
}

const steps = [
  { id: 1, title: "Select Type", description: "Choose integration type" },
  { id: 2, title: "Configure", description: "Set up connection details" },
  { id: 3, title: "Test", description: "Verify connection" },
  { id: 4, title: "Select Data", description: "Choose data to sync" },
  { id: 5, title: "Confirm", description: "Review and activate" }
];

export const IntegrationWizard = ({ onClose }: IntegrationWizardProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [integrationData, setIntegrationData] = useState<IntegrationData>({});

  const progress = (currentStep / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateIntegrationData = (data: Partial<IntegrationData>) => {
    setIntegrationData(prev => ({ ...prev, ...data }));
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <IntegrationTypeSelector 
            onSelect={(type) => {
              updateIntegrationData({ type });
              handleNext();
            }}
          />
        );
      case 2:
        return (
          <IntegrationConfiguration 
            integrationType={integrationData.type}
            onNext={(config) => {
              updateIntegrationData({ config });
              handleNext();
            }}
            onBack={handleBack}
          />
        );
      case 3:
        return (
          <ConnectionTest 
            integrationData={integrationData}
            onNext={(testResults) => {
              updateIntegrationData({ testResults });
              handleNext();
            }}
            onBack={handleBack}
          />
        );
      case 4:
        return (
          <DataMapping 
            integrationData={integrationData}
            onNext={(dataMapping) => {
              updateIntegrationData({ dataMapping });
              handleNext();
            }}
            onBack={handleBack}
          />
        );
      case 5:
        return (
          <IntegrationConfirmation 
            integrationData={integrationData}
            onComplete={onClose}
            onBack={handleBack}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6 flex justify-between items-center" data-tour="wizard-header">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Add New Integration</h1>
            <p className="text-gray-600 mt-2">
              Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.title}
            </p>
          </div>
          <Button variant="outline" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Progress */}
        <div className="mb-8" data-tour="progress-steps">
          <div className="flex justify-between mb-2">
            {steps.map((step) => (
              <div key={step.id} className="flex flex-col items-center">
                <div 
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step.id <= currentStep 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {step.id}
                </div>
                <div className="text-xs mt-1 text-center">
                  <div className="font-medium">{step.title}</div>
                  <div className="text-gray-500">{step.description}</div>
                </div>
              </div>
            ))}
          </div>
          <Progress value={progress} className="w-full" />
        </div>

        {/* Step Content */}
        <Card>
          <CardContent className="p-6">
            {renderStep()}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
