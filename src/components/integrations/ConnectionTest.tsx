
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";
import { IntegrationData } from "./IntegrationWizard";
import { PageWalkthrough } from "@/components/walkthrough/PageWalkthrough";
import { integrationStep3WalkthroughSteps } from "@/data/integrationStep3WalkthroughSteps";

interface ConnectionTestProps {
  integrationData: IntegrationData;
  onNext: (testResults: any) => void;
  onBack: () => void;
}

export const ConnectionTest = ({ integrationData, onNext, onBack }: ConnectionTestProps) => {
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);

  const runConnectionTest = async () => {
    setTesting(true);
    
    // Simulate API testing with random success/failure
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const isSuccess = Math.random() > 0.3; // 70% success rate
    const results = {
      success: isSuccess,
      responseTime: Math.floor(Math.random() * 500) + 100,
      dataPreview: isSuccess ? {
        recordsFound: Math.floor(Math.random() * 1000) + 100,
        sampleData: [
          { date: "2024-01-15", sales: 1250.50, items: 45 },
          { date: "2024-01-14", sales: 980.25, items: 32 }
        ]
      } : null,
      error: !isSuccess ? "Invalid API credentials" : null
    };
    
    setTestResults(results);
    setTesting(false);
  };

  const handleNext = () => {
    onNext(testResults);
  };

  return (
    <>
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Test Connection</h2>
          <p className="text-gray-600">Verify that your integration is configured correctly</p>
        </div>

        <Card data-tour="connection-test">
          <CardHeader>
            <CardTitle>Connection Test</CardTitle>
            <CardDescription>
              We'll test the connection using the provided credentials
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {!testResults && !testing && (
              <div className="text-center py-8">
                <Button onClick={runConnectionTest} size="lg">
                  Test Connection
                </Button>
              </div>
            )}

            {testing && (
              <div className="text-center py-8">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
                <p className="text-gray-600">Testing connection...</p>
              </div>
            )}

            {testResults && (
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  {testResults.success ? (
                    <CheckCircle className="w-6 h-6 text-green-500" />
                  ) : (
                    <XCircle className="w-6 h-6 text-red-500" />
                  )}
                  <div>
                    <h3 className="font-semibold">
                      {testResults.success ? "Connection Successful" : "Connection Failed"}
                    </h3>
                    <p className="text-sm text-gray-600">
                      Response time: {testResults.responseTime}ms
                    </p>
                  </div>
                </div>

                {testResults.error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p className="text-red-700">{testResults.error}</p>
                  </div>
                )}

                {testResults.dataPreview && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4" data-tour="data-preview">
                    <h4 className="font-semibold mb-2">Data Preview</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Found {testResults.dataPreview.recordsFound} records
                    </p>
                    <div className="bg-white rounded border p-3">
                      <pre className="text-xs">
                        {JSON.stringify(testResults.dataPreview.sampleData, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                {!testResults.success && (
                  <Button onClick={runConnectionTest} variant="outline">
                    Try Again
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <Button variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button 
            onClick={handleNext}
            disabled={!testResults?.success}
          >
            Next: Configure Data
          </Button>
        </div>
      </div>

      <PageWalkthrough
        steps={integrationStep3WalkthroughSteps}
        pageKey="integration-step-3"
        autoStart={true}
        triggerClassName="fixed bottom-6 right-6 z-30"
      />
    </>
  );
};
