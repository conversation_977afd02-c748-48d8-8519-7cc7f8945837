
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Play, Settings, Trash2 } from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'error';
  lastSync: string;
  nextSync: string;
  records: number;
  frequency: string;
}

const mockIntegrations: Integration[] = [
  {
    id: '1',
    name: 'Toast POS',
    type: 'ingest',
    status: 'active',
    lastSync: '2 min ago',
    nextSync: 'In 13 min',
    records: 1247,
    frequency: 'Every 15 min'
  },
  {
    id: '2',
    name: 'Square Payment',
    type: 'ingest',
    status: 'active',
    lastSync: '5 min ago',
    nextSync: 'In 10 min',
    records: 892,
    frequency: 'Every 15 min'
  },
  {
    id: '3',
    name: 'QuickBooks Export',
    type: 'publish',
    status: 'active',
    lastSync: '1 hour ago',
    nextSync: 'In 23 hours',
    records: 156,
    frequency: 'Daily'
  },
  {
    id: '4',
    name: 'Customer Analytics',
    type: 'transform',
    status: 'inactive',
    lastSync: '3 days ago',
    nextSync: 'Paused',
    records: 0,
    frequency: 'Hourly'
  },
  {
    id: '5',
    name: 'Inventory Sync',
    type: 'ingest',
    status: 'error',
    lastSync: 'Failed',
    nextSync: 'Retry in 5 min',
    records: 0,
    frequency: 'Every 30 min'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'bg-green-100 text-green-800';
    case 'inactive': return 'bg-gray-100 text-gray-800';
    case 'error': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'ingest': return 'bg-blue-100 text-blue-800';
    case 'publish': return 'bg-green-100 text-green-800';
    case 'transform': return 'bg-purple-100 text-purple-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getActivityBars = (frequency: string, status: string) => {
  const baseColor = status === 'active' ? 'bg-green-400' : status === 'error' ? 'bg-red-400' : 'bg-gray-300';
  
  // Different patterns based on frequency
  if (frequency.includes('15 min') || frequency.includes('30 min')) {
    // Dense pattern for frequent syncs (15-30 minutes)
    return Array.from({ length: 12 }, (_, i) => (
      <div key={i} className={`w-1.5 h-4 rounded-sm ${baseColor}`} />
    ));
  } else if (frequency.includes('Hourly') || frequency.includes('hour')) {
    // Medium spacing for hourly syncs
    return Array.from({ length: 8 }, (_, i) => (
      <div key={i} className={`w-2 h-4 rounded-sm ${i % 2 === 0 ? baseColor : 'bg-gray-200'}`} />
    ));
  } else if (frequency.includes('Daily')) {
    // Sparse pattern for daily syncs
    return Array.from({ length: 7 }, (_, i) => (
      <div key={i} className={`w-2 h-4 rounded-sm ${i === 0 || i === 3 || i === 6 ? baseColor : 'bg-gray-200'}`} />
    ));
  }
  
  // Default pattern
  return Array.from({ length: 7 }, (_, i) => (
    <div key={i} className={`w-2 h-4 rounded-sm ${baseColor}`} />
  ));
};

export const IntegrationsTable = () => {
  return (
    <Card data-tour="integrations-table">
      <CardHeader>
        <CardTitle>Active Integrations</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4">Integration</th>
                <th className="text-left py-3 px-4" data-tour="integration-types">Type</th>
                <th className="text-left py-3 px-4">Status</th>
                <th className="text-left py-3 px-4">Last Sync</th>
                <th className="text-left py-3 px-4">Next Sync</th>
                <th className="text-left py-3 px-4">Records</th>
                <th className="text-left py-3 px-4" data-tour="activity-bars">Activity Pattern</th>
                <th className="text-left py-3 px-4" data-tour="integration-actions">Actions</th>
              </tr>
            </thead>
            <tbody>
              {mockIntegrations.map((integration) => (
                <tr key={integration.id} className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4">
                    <div>
                      <div className="font-medium">{integration.name}</div>
                      <div className="text-gray-500 text-xs">{integration.frequency}</div>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <Badge variant="secondary" className={getTypeColor(integration.type)}>
                      {integration.type}
                    </Badge>
                  </td>
                  <td className="py-3 px-4">
                    <Badge variant="secondary" className={getStatusColor(integration.status)}>
                      {integration.status}
                    </Badge>
                  </td>
                  <td className="py-3 px-4 text-gray-600">{integration.lastSync}</td>
                  <td className="py-3 px-4 text-gray-600">{integration.nextSync}</td>
                  <td className="py-3 px-4 text-gray-600">{integration.records.toLocaleString()}</td>
                  <td className="py-3 px-4">
                    <div className="flex gap-1 items-center">
                      {getActivityBars(integration.frequency, integration.status)}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm">
                        <Settings className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Play className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};
