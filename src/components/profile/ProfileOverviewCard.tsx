
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Mail, Building, Shield } from "lucide-react";

interface ProfileOverviewCardProps {
  fullName: string;
  email: string;
  role: string;
  companyName?: string;
  companyIndustry?: string;
}

export function ProfileOverviewCard({ 
  fullName, 
  email, 
  role, 
  companyName, 
  companyIndustry 
}: ProfileOverviewCardProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'CEO': return 'bg-purple-100 text-purple-800';
      case 'COO': return 'bg-blue-100 text-blue-800';
      case 'Data Admin': return 'bg-green-100 text-green-800';
      case 'Business Admin': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="lg:col-span-1">
      <CardHeader className="text-center pb-4">
        <div className="flex justify-center mb-4">
          <Avatar className="w-24 h-24">
            <AvatarImage src="" alt={fullName} />
            <AvatarFallback className="text-lg font-semibold bg-blue-100 text-blue-800">
              {getInitials(fullName || email)}
            </AvatarFallback>
          </Avatar>
        </div>
        <CardTitle className="text-xl">{fullName || 'User'}</CardTitle>
        <div className="flex justify-center mt-2">
          <Badge className={getRoleBadgeColor(role)}>
            {role}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-3 text-sm text-gray-600">
          <Mail className="w-4 h-4" />
          <span>{email}</span>
        </div>
        <div className="flex items-center space-x-3 text-sm text-gray-600">
          <Building className="w-4 h-4" />
          <span>{companyName || 'No Company'}</span>
        </div>
        <div className="flex items-center space-x-3 text-sm text-gray-600">
          <Shield className="w-4 h-4" />
          <span>{companyIndustry || 'Industry Not Set'}</span>
        </div>
      </CardContent>
    </Card>
  );
}
