
import { supabase } from '@/integrations/supabase/client';
import { AIInsight, AIInsightsFilters } from '@/types/aiAnalytics';

export class InsightsService {
  static async getAIInsights(tenantKey: number = 1, filters?: AIInsightsFilters): Promise<AIInsight[]> {
    console.log('Fetching AI insights...', filters);
    
    let query = supabase
      .from('ai_insights')
      .select(`
        *,
        dim_location!inner(location_name)
      `)
      .eq('tenant_key', tenantKey)
      .eq('status', 'active')
      .order('created_date', { ascending: false });

    if (filters?.type) {
      query = query.eq('insight_type', filters.type);
    }
    if (filters?.category) {
      query = query.eq('category', filters.category);
    }
    if (filters?.severity) {
      query = query.eq('severity', filters.severity);
    }
    if (filters?.locationKey) {
      query = query.eq('location_key', filters.locationKey);
    }

    const { data, error } = await query.limit(50);

    if (error) {
      console.error('Error fetching AI insights:', error);
      throw error;
    }

    // Cast the raw data to proper types and handle JSONB fields
    return (data || []).map(item => ({
      insight_key: item.insight_key,
      insight_id: item.insight_id,
      insight_type: item.insight_type as 'anomaly' | 'prediction' | 'recommendation' | 'trend',
      category: item.category as 'revenue' | 'operations' | 'customer' | 'inventory' | 'labor',
      title: item.title,
      description: item.description,
      severity: item.severity as 'low' | 'medium' | 'high' | 'critical',
      confidence_score: item.confidence_score,
      impact_score: item.impact_score,
      location_key: item.location_key,
      insight_data: item.insight_data,
      recommended_actions: Array.isArray(item.recommended_actions) 
        ? item.recommended_actions.map(action => 
            typeof action === 'string' ? action : String(action)
          )
        : (item.recommended_actions ? [String(item.recommended_actions)] : []),
      status: item.status as 'active' | 'resolved' | 'dismissed',
      created_date: item.created_date
    }));
  }

  static async markInsightAsResolved(insightKey: number, userId?: string): Promise<boolean> {
    console.log('Marking insight as resolved:', insightKey);
    
    const { error } = await supabase
      .from('ai_insights')
      .update({
        status: 'resolved',
        resolved_by: userId,
        resolved_at: new Date().toISOString()
      })
      .eq('insight_key', insightKey);

    if (error) {
      console.error('Error marking insight as resolved:', error);
      return false;
    }

    return true;
  }
}
