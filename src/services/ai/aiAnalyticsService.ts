
import { InsightsService } from './insightsService';
import { AnomalyService } from './anomalyService';
import { PredictiveService } from './predictiveService';
import { MetricsService } from './metricsService';

// Re-export types for backward compatibility
export type { AIInsight, AnomalyDetection, PredictiveAnalytics, BIMetrics, AIInsightsFilters } from '@/types/aiAnalytics';

// Main service class that delegates to specialized services
export class AIAnalyticsService {
  // Insights methods
  static async getAIInsights(tenantKey: number = 1, filters?: {
    type?: string;
    category?: string;
    severity?: string;
    locationKey?: number;
  }) {
    return InsightsService.getAIInsights(tenantKey, filters);
  }

  static async markInsightAsResolved(insightKey: number, userId?: string) {
    return InsightsService.markInsightAsResolved(insightKey, userId);
  }

  // Anomaly methods
  static async getAnomalyDetections(tenantKey: number = 1, locationKey?: number) {
    return AnomalyService.getAnomalyDetections(tenantKey, locationKey);
  }

  static async confirmAnomaly(anomalyKey: number, userId?: string) {
    return AnomalyService.confirmAnomaly(anomalyKey, userId);
  }

  // Predictive analytics methods
  static async getPredictiveAnalytics(tenantKey: number = 1, predictionType?: string) {
    return PredictiveService.getPredictiveAnalytics(tenantKey, predictionType);
  }

  // Metrics methods
  static async getBIMetrics(tenantKey: number = 1, category?: string) {
    return MetricsService.getBIMetrics(tenantKey, category);
  }
}
