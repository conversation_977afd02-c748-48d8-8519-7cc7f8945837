
import { supabase } from '@/integrations/supabase/client';
import { PredictiveAnalytics } from '@/types/aiAnalytics';

export class PredictiveService {
  static async getPredictiveAnalytics(tenantKey: number = 1, predictionType?: string): Promise<PredictiveAnalytics[]> {
    console.log('Fetching predictive analytics...');
    
    let query = supabase
      .from('predictive_analytics')
      .select(`
        *,
        dim_location(location_name)
      `)
      .eq('tenant_key', tenantKey)
      .gte('forecast_date', new Date().toISOString().split('T')[0])
      .order('forecast_date', { ascending: true });

    if (predictionType) {
      query = query.eq('prediction_type', predictionType);
    }

    const { data, error } = await query.limit(20);

    if (error) {
      console.error('Error fetching predictive analytics:', error);
      throw error;
    }

    // Cast the raw data to proper types
    return (data || []).map(item => ({
      ...item,
      prediction_type: item.prediction_type as 'sales_forecast' | 'demand_forecast' | 'labor_optimization'
    }));
  }
}
