
import { supabase } from '@/integrations/supabase/client';

export class EnhancedETLService {
  // Populate enhanced dimension tables with sample data
  static async populateSampleData() {
    console.log('Populating enhanced dimension tables with sample data...');
    
    try {
      // Get tenant key
      const { data: tenant } = await supabase
        .from('dim_tenant')
        .select('tenant_key')
        .eq('tenant_name', 'CONVX Restaurant Group')
        .single();

      if (!tenant) throw new Error('Tenant not found');

      const tenantKey = tenant.tenant_key;

      // Populate dim_order_type
      const orderTypes = [
        { tenant_key: tenantKey, order_type_id: 'dine_in', order_type_name: 'Dine In', channel: 'in_store', service_charge_percentage: 0, average_service_time_minutes: 45 },
        { tenant_key: tenantKey, order_type_id: 'takeout', order_type_name: 'Takeout', channel: 'in_store', service_charge_percentage: 0, average_service_time_minutes: 15 },
        { tenant_key: tenantKey, order_type_id: 'delivery', order_type_name: 'Delivery', channel: 'online', service_charge_percentage: 2.5, average_service_time_minutes: 35 },
        { tenant_key: tenantKey, order_type_id: 'drive_thru', order_type_name: 'Drive Thru', channel: 'drive_thru', service_charge_percentage: 0, average_service_time_minutes: 8 }
      ];

      await supabase.from('dim_order_type').upsert(orderTypes);

      // Populate dim_payment_method
      const paymentMethods = [
        { tenant_key: tenantKey, payment_method_id: 'cash', payment_type: 'Cash', payment_provider: null, processing_fee_percentage: 0, is_digital: false },
        { tenant_key: tenantKey, payment_method_id: 'credit_card', payment_type: 'Credit Card', payment_provider: 'Stripe', processing_fee_percentage: 2.9, is_digital: true },
        { tenant_key: tenantKey, payment_method_id: 'debit_card', payment_type: 'Debit Card', payment_provider: 'Stripe', processing_fee_percentage: 1.5, is_digital: true },
        { tenant_key: tenantKey, payment_method_id: 'mobile_pay', payment_type: 'Mobile Payment', payment_provider: 'Apple Pay', processing_fee_percentage: 2.5, is_digital: true }
      ];

      await supabase.from('dim_payment_method').upsert(paymentMethods);

      // Populate dim_promotion
      const promotions = [
        { 
          tenant_key: tenantKey, 
          promotion_id: 'happy_hour', 
          promotion_name: 'Happy Hour 20% Off', 
          promotion_type: 'discount', 
          discount_type: 'percentage', 
          discount_value: 20,
          start_date: '2024-01-01',
          end_date: '2024-12-31',
          channel: 'all'
        },
        { 
          tenant_key: tenantKey, 
          promotion_id: 'loyalty_10', 
          promotion_name: 'Loyalty Member 10% Off', 
          promotion_type: 'loyalty', 
          discount_type: 'percentage', 
          discount_value: 10,
          channel: 'all'
        }
      ];

      await supabase.from('dim_promotion').upsert(promotions);

      // Update existing dim_date and dim_time with tenant_key
      await supabase
        .from('dim_date')
        .update({ tenant_key: tenantKey })
        .is('tenant_key', null);

      await supabase
        .from('dim_time')
        .update({ tenant_key: tenantKey })
        .is('tenant_key', null);

      // Generate sample fact_labor data
      const laborData = [];
      const { data: employees } = await supabase.from('dim_employee').select('employee_key');
      const { data: locations } = await supabase.from('dim_location').select('location_key');

      if (employees && locations) {
        for (let i = 0; i < 100; i++) {
          const employee = employees[Math.floor(Math.random() * employees.length)];
          const location = locations[Math.floor(Math.random() * locations.length)];
          
          laborData.push({
            tenant_key: tenantKey,
            employee_key: employee.employee_key,
            location_key: location.location_key,
            date_key: 20240101 + i,
            scheduled_hours: 8,
            actual_hours: 7.5 + Math.random(),
            labor_cost: 120 + Math.random() * 40,
            shift_type: ['morning', 'afternoon', 'evening'][Math.floor(Math.random() * 3)],
            productivity_score: 0.8 + Math.random() * 0.2
          });
        }

        await supabase.from('fact_labor').upsert(laborData);
      }

      console.log('Sample data populated successfully');
      return { success: true, message: 'Enhanced dimension tables populated with sample data' };

    } catch (error) {
      console.error('Error populating sample data:', error);
      return { success: false, error: error.message };
    }
  }

  // Enhanced analytics aggregation
  static async generateAnalyticsAggregates() {
    console.log('Generating analytics aggregates...');
    
    try {
      // This would typically run complex aggregation queries
      // For now, we'll simulate the process
      
      const aggregates = {
        dailyRevenue: await this.calculateDailyRevenue(),
        locationPerformance: await this.calculateLocationPerformance(),
        customerSegments: await this.calculateCustomerSegments(),
        productPerformance: await this.calculateProductPerformance()
      };

      console.log('Analytics aggregates generated:', aggregates);
      return { success: true, aggregates };

    } catch (error) {
      console.error('Error generating analytics aggregates:', error);
      return { success: false, error: error.message };
    }
  }

  private static async calculateDailyRevenue() {
    // Mock calculation - in real implementation, this would be a complex SQL query
    return {
      totalRevenue: 2847650.00,
      averageDailyRevenue: 94921.67,
      revenueGrowth: 15.2
    };
  }

  private static async calculateLocationPerformance() {
    return {
      topPerformingLocation: 'Airport Location',
      averagePerformanceScore: 92.2,
      locationsAboveTarget: 2,
      locationsBelowTarget: 1
    };
  }

  private static async calculateCustomerSegments() {
    return {
      totalCustomers: 41780,
      loyaltyTierDistribution: {
        bronze: 65,
        silver: 25,
        gold: 8,
        platinum: 2
      },
      averageLifetimeValue: 486.50
    };
  }

  private static async calculateProductPerformance() {
    return {
      topSellingItem: 'Classic Burger',
      averageMargin: 58.7,
      lowPerformingItems: 3,
      seasonalTrends: 'positive'
    };
  }
}
