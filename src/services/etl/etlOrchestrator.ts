
import { BaseETLService } from './baseETLService';
import { LocationETLService } from './locationETLService';
import { MenuItemETLService } from './menuItemETLService';
import { EmployeeETLService } from './employeeETLService';
import { SalesETLService } from './salesETLService';

export class ETLOrchestrator extends BaseETLService {
  static async runFullETL() {
    console.log('Starting enhanced full ETL process...');
    
    const results = {
      locations: await LocationETLService.syncLocations(),
      menuItems: await MenuItemETLService.syncMenuItems(),
      employees: await EmployeeETLService.syncEmployees(),
      transactions: await SalesETLService.syncSalesTransactions()
    };

    // Log the ETL process with enhanced tracking
    const totalRecords = Object.values(results).reduce((sum, result) => 
      sum + (result.recordsProcessed || 0), 0
    );

    const hasErrors = Object.values(results).some(result => !result.success);

    await this.logETLProcess(
      'enhanced_etl_process',
      'Enhanced Full ETL Sync',
      hasErrors ? 'completed_with_errors' : 'completed',
      totalRecords,
      hasErrors ? 1 : 0,
      hasErrors ? { results } : null
    );

    return results;
  }

  // Export individual services for direct access
  static get LocationService() { return LocationETLService; }
  static get MenuItemService() { return MenuItemETLService; }
  static get EmployeeService() { return EmployeeETLService; }
  static get SalesService() { return SalesETLService; }
}
