
import { supabase } from '@/integrations/supabase/client';
import { BaseETLService } from './baseETLService';

export class MenuItemETLService extends BaseETLService {
  static async syncMenuItems() {
    console.log('Starting menu item sync...');
    
    try {
      // Get all menu items from operational table
      const { data: menuItems } = await supabase
        .from('menu_items')
        .select('*');

      if (!menuItems) return this.createETLResult(false, 0, 'No menu items found');

      // Upsert into dim_menu_item
      const dimMenuItems = menuItems.map(item => ({
        menu_item_id: item.id,
        item_name: item.name,
        category: item.category,
        subcategory: null, // Can be enhanced later
        price_tier: item.price > 20 ? 'premium' : item.price > 10 ? 'standard' : 'value',
        cost_tier: item.cost > 15 ? 'high' : item.cost > 8 ? 'medium' : 'low',
        is_active: item.is_active
      }));

      const { error } = await supabase
        .from('dim_menu_item')
        .upsert(dimMenuItems, { onConflict: 'menu_item_id' });

      if (error) throw error;

      console.log(`Synced ${menuItems.length} menu items to data warehouse`);
      return this.createETLResult(true, menuItems.length);
    } catch (error) {
      console.error('Menu item sync failed:', error);
      return this.createETLResult(false, 0, error.message);
    }
  }
}
