
import { supabase } from '@/integrations/supabase/client';
import { BaseETLService } from './baseETLService';

export class EmployeeETLService extends BaseETLService {
  static async syncEmployees() {
    console.log('Starting employee sync...');
    
    try {
      // Get all employees from master data
      const { data: employees } = await supabase
        .from('master_employees')
        .select('*, master_locations(location_key:location_id)');

      if (!employees) return this.createETLResult(false, 0, 'No employees found');

      // First get location mappings
      const { data: locationMappings } = await supabase
        .from('dim_location')
        .select('location_key, location_id');

      const locationMap = new Map(
        locationMappings?.map(loc => [loc.location_id, loc.location_key]) || []
      );

      // Upsert into dim_employee
      const dimEmployees = employees.map(emp => ({
        employee_id: emp.id,
        employee_name: emp.name,
        role: emp.role,
        department: emp.department,
        location_key: emp.location_id ? locationMap.get(emp.location_id) : null,
        hire_date: null, // Can be enhanced later
        is_active: true
      }));

      const { error } = await supabase
        .from('dim_employee')
        .upsert(dimEmployees, { onConflict: 'employee_id' });

      if (error) throw error;

      console.log(`Synced ${employees.length} employees to data warehouse`);
      return this.createETLResult(true, employees.length);
    } catch (error) {
      console.error('Employee sync failed:', error);
      return this.createETLResult(false, 0, error.message);
    }
  }
}
