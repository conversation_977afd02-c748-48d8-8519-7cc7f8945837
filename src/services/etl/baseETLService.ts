
import { supabase } from '@/integrations/supabase/client';

export abstract class BaseETLService {
  protected static async logETLProcess(
    sourceType: string,
    sourceName: string,
    status: 'completed' | 'completed_with_errors' | 'failed',
    recordsProcessed: number,
    recordsFailed: number = 0,
    errorDetails?: any
  ) {
    try {
      await supabase.from('data_ingestion_logs').insert({
        source_type: sourceType,
        source_name: sourceName,
        status,
        records_processed: recordsProcessed,
        records_failed: recordsFailed,
        error_details: errorDetails,
        completed_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to log ETL process:', error);
    }
  }

  protected static createETLResult(success: boolean, recordsProcessed: number = 0, error?: string) {
    return {
      success,
      recordsProcessed,
      error
    };
  }
}
