
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useUserProfile } from '@/hooks/useUserProfile';
import { useCompany } from '@/hooks/useCompany';
import { OnboardingContextType } from '@/types/onboarding';

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const { user: authUser } = useAuth();
  const { profile, updateProfile, completeOnboarding } = useUserProfile();
  const { company, createCompany } = useCompany();
  const [currentStep, setCurrentStep] = useState(1);

  useEffect(() => {
    if (profile) {
      setCurrentStep(profile.onboardingStep || 1);
    }
  }, [profile]);

  const isNewUser = !profile?.companyId && !profile?.invitedBy;

  const setCurrentUser = async (user: any) => {
    if (profile) {
      await updateProfile(user);
    }
  };

  const setCompany = async (companyData: any) => {
    if (!company) {
      await createCompany(companyData);
    }
  };

  const handleCompleteOnboarding = async () => {
    await completeOnboarding();
  };

  const contextValue: OnboardingContextType = {
    currentUser: profile,
    company,
    isNewUser,
    currentStep,
    setCurrentStep,
    setCurrentUser,
    setCompany,
    completeOnboarding: handleCompleteOnboarding
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};
