
import { Invoice } from '@/types/invoice';

export const sampleInvoice: Invoice = {
  id: '1',
  invoiceNumber: 'INV-2024-001',
  date: '2024-01-15',
  dueDate: '2024-02-14',
  status: 'sent',
  company: {
    name: 'CONVX Data Solutions',
    address: {
      street: '123 Business Ave',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94107'
    },
    email: '<EMAIL>',
    phone: '(*************',
    website: 'www.convxdata.com'
  },
  client: {
    name: '<PERSON>',
    company: 'Smith Restaurant Group',
    email: '<EMAIL>',
    address: {
      street: '456 Client Street',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210'
    }
  },
  items: [
    {
      id: '1',
      description: 'Data Platform Setup & Configuration',
      quantity: 1,
      rate: 2500.00,
      amount: 2500.00
    },
    {
      id: '2',
      description: 'Custom Analytics Dashboard Development',
      quantity: 40,
      rate: 125.00,
      amount: 5000.00
    },
    {
      id: '3',
      description: 'Integration Development (POS Systems)',
      quantity: 20,
      rate: 150.00,
      amount: 3000.00
    },
    {
      id: '4',
      description: 'Data Migration & ETL Pipeline Setup',
      quantity: 30,
      rate: 135.00,
      amount: 4050.00
    },
    {
      id: '5',
      description: 'Training & Documentation',
      quantity: 16,
      rate: 100.00,
      amount: 1600.00
    }
  ],
  subtotal: 16150.00,
  taxRate: 0.08,
  taxAmount: 1292.00,
  total: 17442.00,
  notes: 'Thank you for choosing CONVX Data Solutions. This invoice covers all development work completed to date for your restaurant data platform implementation. Payment terms are Net 30 days.'
};
